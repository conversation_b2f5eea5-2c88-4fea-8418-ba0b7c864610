#!/usr/bin/env python3
"""
Test script to verify scalping endpoints work correctly
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def test_scalping_endpoints():
    """Test scalping endpoints"""
    print("🚀 Testing Scalping Endpoints\n")
    
    # Test scalping status endpoint
    print("🔍 Testing /api/scalping/status...")
    try:
        response = requests.get(f"{BASE_URL}/api/scalping/status")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Scalping Status: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Scalping Status failed: {response.text}")
    except Exception as e:
        print(f"❌ Scalping Status error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test scalping account endpoint
    print("🔍 Testing /api/scalping/account...")
    try:
        response = requests.get(f"{BASE_URL}/api/scalping/account")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Scalping Account: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Scalping Account failed: {response.text}")
    except Exception as e:
        print(f"❌ Scalping Account error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test scalping positions endpoint
    print("🔍 Testing /api/scalping/positions...")
    try:
        response = requests.get(f"{BASE_URL}/api/scalping/positions")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Scalping Positions: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Scalping Positions failed: {response.text}")
    except Exception as e:
        print(f"❌ Scalping Positions error: {e}")

if __name__ == "__main__":
    test_scalping_endpoints()
