"""
PRICE MONITOR
- Fungsi cek % pergerakan harga
- Cek price tiap 5 menit dan bandingkan terhadap moving window 15 menit
- Real-time price tracking untuk trigger system
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import deque
from backend.services.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)

class PriceMonitor:
    """
    Monitor price movements with moving window analysis
    """
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
        
        # Price history storage (symbol -> deque of price data)
        self.price_history = {}  # symbol -> deque([{price, timestamp}, ...])
        
        # Moving window settings
        self.window_minutes = 15  # 15-minute moving window
        self.check_interval_seconds = 300  # Check every 5 minutes
        self.max_history_points = 20  # Keep last 20 price points per symbol
        
        # Price movement tracking
        self.last_significant_moves = {}  # symbol -> {price, timestamp, direction}
        
    async def start_monitoring(self, symbols: List[str]):
        """Start price monitoring for given symbols"""
        logger.info(f"📊 Starting price monitor for symbols: {symbols}")
        
        # Initialize price history
        for symbol in symbols:
            self.price_history[symbol] = deque(maxlen=self.max_history_points)
            await self._initialize_price_history(symbol)
        
        # Start monitoring loop
        while True:
            try:
                for symbol in symbols:
                    await self._update_price_data(symbol)
                
                # Wait before next check
                await asyncio.sleep(self.check_interval_seconds)
                
            except Exception as e:
                logger.error(f"❌ Error in price monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _initialize_price_history(self, symbol: str):
        """Initialize price history for symbol"""
        try:
            current_price = await self.data_fetcher.get_current_price(symbol)
            if current_price:
                price_data = {
                    "price": current_price,
                    "timestamp": datetime.now()
                }
                self.price_history[symbol].append(price_data)
                logger.info(f"📊 Initialized price history for {symbol}: ${current_price:.5f}")
            
        except Exception as e:
            logger.error(f"❌ Error initializing price history for {symbol}: {e}")
    
    async def _update_price_data(self, symbol: str):
        """Update price data for symbol"""
        try:
            current_price = await self.data_fetcher.get_current_price(symbol)
            if not current_price:
                return
            
            # Add new price data
            price_data = {
                "price": current_price,
                "timestamp": datetime.now()
            }
            self.price_history[symbol].append(price_data)
            
            # Analyze price movement
            movement_analysis = self._analyze_price_movement(symbol)
            
            if movement_analysis["significant_move"]:
                logger.info(f"📈 Significant move detected for {symbol}:")
                logger.info(f"   💰 Price: ${current_price:.5f}")
                logger.info(f"   📊 Movement: {movement_analysis['movement_percent']:.2f}%")
                logger.info(f"   ⏰ Window: {movement_analysis['window_minutes']} minutes")
                
                # Update last significant move
                self.last_significant_moves[symbol] = {
                    "price": current_price,
                    "timestamp": datetime.now(),
                    "direction": movement_analysis["direction"],
                    "movement_percent": movement_analysis["movement_percent"]
                }
            
        except Exception as e:
            logger.error(f"❌ Error updating price data for {symbol}: {e}")
    
    def _analyze_price_movement(self, symbol: str) -> Dict[str, Any]:
        """Analyze price movement within moving window"""
        try:
            if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
                return {
                    "significant_move": False,
                    "movement_percent": 0.0,
                    "direction": "neutral",
                    "window_minutes": 0
                }
            
            price_data = list(self.price_history[symbol])
            current_time = datetime.now()
            current_price = price_data[-1]["price"]
            
            # Find reference price from 15 minutes ago
            reference_price = None
            window_start = current_time - timedelta(minutes=self.window_minutes)
            
            # Find the closest price to window start
            for data in reversed(price_data[:-1]):  # Exclude current price
                if data["timestamp"] <= window_start:
                    reference_price = data["price"]
                    break
            
            # If no price from 15 minutes ago, use oldest available
            if reference_price is None and len(price_data) > 1:
                reference_price = price_data[0]["price"]
            
            if reference_price is None:
                return {
                    "significant_move": False,
                    "movement_percent": 0.0,
                    "direction": "neutral",
                    "window_minutes": 0
                }
            
            # Calculate movement percentage
            movement_percent = self.price_moved_enough_percent(current_price, reference_price)
            
            # Determine direction
            direction = "up" if current_price > reference_price else "down" if current_price < reference_price else "neutral"
            
            # Check if movement is significant (>= 0.1% for most pairs)
            significant_thresholds = {
                "BTCUSD": 0.2,
                "BTCUSDm": 0.2,
                "ETHUSD": 0.25,
                "ETHUSDm": 0.25,
                "EURUSDm": 0.05,
                "XAUUSDm": 0.15,
                "USDAED": 0.03
            }
            
            threshold = significant_thresholds.get(symbol, 0.1)
            significant_move = abs(movement_percent) >= threshold
            
            return {
                "significant_move": significant_move,
                "movement_percent": movement_percent,
                "direction": direction,
                "window_minutes": self.window_minutes,
                "threshold": threshold,
                "reference_price": reference_price,
                "current_price": current_price
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing price movement for {symbol}: {e}")
            return {
                "significant_move": False,
                "movement_percent": 0.0,
                "direction": "neutral",
                "window_minutes": 0
            }
    
    def price_moved_enough(
        self,
        current_price: float,
        reference_price: float,
        threshold_percent: float
    ) -> bool:
        """
        Check if price moved enough to trigger analysis
        """
        if reference_price == 0:
            return False
        
        movement = abs(current_price - reference_price) / reference_price * 100
        return movement >= threshold_percent
    
    def price_moved_enough_percent(
        self,
        current_price: float,
        reference_price: float
    ) -> float:
        """
        Calculate percentage movement between prices
        """
        if reference_price == 0:
            return 0.0
        
        movement = ((current_price - reference_price) / reference_price) * 100
        return round(movement, 3)
    
    async def get_price_movement_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get current price movement analysis for symbol"""
        try:
            # Update current price
            await self._update_price_data(symbol)
            
            # Get movement analysis
            movement_analysis = self._analyze_price_movement(symbol)
            
            # Get price history summary
            price_data = list(self.price_history.get(symbol, []))
            
            history_summary = {
                "total_points": len(price_data),
                "oldest_timestamp": price_data[0]["timestamp"].isoformat() if price_data else None,
                "newest_timestamp": price_data[-1]["timestamp"].isoformat() if price_data else None,
                "price_range": {
                    "min": min(data["price"] for data in price_data) if price_data else 0,
                    "max": max(data["price"] for data in price_data) if price_data else 0
                }
            }
            
            return {
                "symbol": symbol,
                "movement_analysis": movement_analysis,
                "history_summary": history_summary,
                "last_significant_move": self.last_significant_moves.get(symbol),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting price movement analysis for {symbol}: {e}")
            return {"error": str(e)}
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        status = {
            "monitored_symbols": list(self.price_history.keys()),
            "window_minutes": self.window_minutes,
            "check_interval_seconds": self.check_interval_seconds,
            "max_history_points": self.max_history_points,
            "price_history_counts": {
                symbol: len(history) 
                for symbol, history in self.price_history.items()
            },
            "last_significant_moves": {
                symbol: {
                    **move_data,
                    "timestamp": move_data["timestamp"].isoformat() if isinstance(move_data["timestamp"], datetime) else move_data["timestamp"]
                }
                for symbol, move_data in self.last_significant_moves.items()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return status
    
    async def manual_price_check(self, symbol: str) -> Dict[str, Any]:
        """Manually check current price and movement for symbol"""
        try:
            current_price = await self.data_fetcher.get_current_price(symbol)
            if not current_price:
                return {"error": "Failed to get current price"}
            
            # Get movement analysis
            movement_analysis = self._analyze_price_movement(symbol)
            
            # Get recent price history
            recent_history = []
            if symbol in self.price_history:
                recent_data = list(self.price_history[symbol])[-5:]  # Last 5 points
                recent_history = [
                    {
                        "price": data["price"],
                        "timestamp": data["timestamp"].isoformat()
                    }
                    for data in recent_data
                ]
            
            return {
                "symbol": symbol,
                "current_price": current_price,
                "movement_analysis": movement_analysis,
                "recent_history": recent_history,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error in manual price check for {symbol}: {e}")
            return {"error": str(e)}
    
    def clear_history(self, symbol: Optional[str] = None):
        """Clear price history for symbol or all symbols"""
        if symbol:
            if symbol in self.price_history:
                self.price_history[symbol].clear()
                logger.info(f"🧹 Cleared price history for {symbol}")
        else:
            self.price_history.clear()
            self.last_significant_moves.clear()
            logger.info("🧹 Cleared all price history")
