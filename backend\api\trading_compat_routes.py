"""
Trading System Compatibility Routes for Scalping Mode
Provides the same endpoints as the original trading system but using scalping services
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException
from loguru import logger

# Import scalping services
from ..services.data_fetcher import DataFetcher
from ..services.scalping_engine import ScalpingEngine

router = APIRouter()

# Global service instances (will be set by main.py)
scalping_engine: Optional[ScalpingEngine] = None
data_fetcher: Optional[DataFetcher] = None

def set_services(engine: ScalpingEngine, fetcher: DataFetcher):
    """Set service instances"""
    global scalping_engine, data_fetcher
    scalping_engine = engine
    data_fetcher = fetcher

@router.post("/start")
async def start_trading_engine(request: dict = None):
    """Start trading engine - Compatible with original trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        if scalping_engine.is_running:
            return {"message": "Trading engine started successfully", "status": "running"}
        
        # Start engine in background
        import asyncio
        asyncio.create_task(scalping_engine.start_engine())
        
        return {"message": "Trading engine started successfully", "status": "running"}
        
    except Exception as e:
        logger.error(f"❌ Error starting trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop")
async def stop_trading_engine():
    """Stop trading engine - Compatible with original trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        if not scalping_engine.is_running:
            return {"message": "Trading engine stopped successfully", "status": "stopped"}
        
        await scalping_engine.stop_engine()
        return {"message": "Trading engine stopped successfully", "status": "stopped"}
        
    except Exception as e:
        logger.error(f"❌ Error stopping trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_system_status():
    """Get system status - Compatible with original trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        # Get account info for compatibility
        account_info = None
        if data_fetcher:
            try:
                mt5_connected = await data_fetcher.connect_mt5()
                if mt5_connected:
                    import MetaTrader5 as mt5
                    account = mt5.account_info()
                    if account:
                        account_info = {
                            "login": account.login,
                            "balance": account.balance,
                            "equity": account.equity,
                            "profit": account.profit,
                            "currency": account.currency
                        }
            except Exception as e:
                logger.warning(f"Failed to get account info for status: {e}")
        
        # Return in trading system format
        return {
            "is_running": scalping_engine.is_running,
            "auto_trading_enabled": True,  # Scalping is always auto
            "monitored_symbols": scalping_engine.monitored_symbols,
            "daily_profit": account_info.get("profit", 0.0) if account_info else 0.0,
            "last_analysis_time": datetime.now().isoformat(),
            "account_balance": account_info.get("balance", 0.0) if account_info else 0.0,
            "account_equity": account_info.get("equity", 0.0) if account_info else 0.0,
            "system_type": "scalping"
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/account")
async def get_account_info():
    """Get account information - Compatible with original trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        # Connect to MT5 and get account info
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            raise HTTPException(status_code=503, detail="MT5 not connected")
        
        import MetaTrader5 as mt5
        account_info = mt5.account_info()
        if not account_info:
            raise HTTPException(status_code=503, detail="Failed to get account info")
        
        # Return in the same format as trading system
        return {
            "login": account_info.login,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "free_margin": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "profit": account_info.profit,
            "currency": account_info.currency,
            "leverage": account_info.leverage,
            "server": account_info.server
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting account info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions")
async def get_positions():
    """Get all positions - Compatible with original trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        # Connect to MT5 and get positions
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            return []  # Return empty list if not connected
        
        import MetaTrader5 as mt5
        positions = mt5.positions_get()
        if not positions:
            return []
        
        position_list = []
        for pos in positions:
            # Get current price for P&L calculation
            tick = mt5.symbol_info_tick(pos.symbol)
            current_price = tick.bid if pos.type == 0 else tick.ask if tick else pos.price_current
            
            # Calculate profit
            profit = pos.profit + pos.swap + getattr(pos, 'commission', 0.0)
            
            position_list.append({
                "ticket": pos.ticket,
                "symbol": pos.symbol,
                "order_type": "BUY" if pos.type == 0 else "SELL",
                "lot_size": pos.volume,
                "entry_price": pos.price_open,
                "current_price": current_price,
                "stop_loss": pos.sl if pos.sl != 0 else None,
                "take_profit": pos.tp if pos.tp != 0 else None,
                "open_time": datetime.fromtimestamp(pos.time).isoformat(),
                "status": "OPEN",
                "profit": profit,
                "commission": getattr(pos, 'commission', 0.0),
                "swap": getattr(pos, 'swap', 0.0),
                "comment": getattr(pos, 'comment', '')
            })
        
        return position_list
        
    except Exception as e:
        logger.error(f"❌ Error getting positions: {e}")
        return []  # Return empty list on error

@router.post("/positions/close")
async def close_position(request: dict):
    """Close position - Compatible with original trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        ticket = request.get("ticket")
        reason = request.get("reason", "Manual close")
        
        if not ticket:
            raise HTTPException(status_code=400, detail="Ticket number required")
        
        # Connect to MT5 and close position
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            raise HTTPException(status_code=503, detail="MT5 not connected")
        
        import MetaTrader5 as mt5
        
        # Get position info
        positions = mt5.positions_get(ticket=ticket)
        if not positions:
            raise HTTPException(status_code=404, detail=f"Position {ticket} not found")
        
        position = positions[0]
        
        # Prepare close request
        close_request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": mt5.ORDER_TYPE_SELL if position.type == 0 else mt5.ORDER_TYPE_BUY,
            "position": ticket,
            "deviation": 20,
            "magic": 0,
            "comment": f"Close: {reason}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        # Send close order
        result = mt5.order_send(close_request)
        
        if result.retcode != mt5.TRADE_RETCODE_DONE:
            raise HTTPException(status_code=400, detail=f"Failed to close position: {result.comment}")
        
        return {
            "message": f"Position {ticket} closed successfully",
            "ticket": ticket,
            "reason": reason,
            "close_price": result.price,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error closing position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/market-data/{symbol}")
async def get_market_data(symbol: str):
    """Get market data for symbol - Compatible with original trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        # Get scalping data which includes market data
        scalping_data = await data_fetcher.get_scalping_data(symbol, "M15")
        
        if not scalping_data:
            raise HTTPException(status_code=404, detail=f"No market data available for {symbol}")
        
        # Convert to trading system format
        return {
            "symbol": symbol,
            "current_price": scalping_data.get("current_price", 0),
            "rsi": scalping_data.get("rsi", 0),
            "atr": scalping_data.get("atr", 0),
            "volume_metrics": scalping_data.get("volume_metrics", {}),
            "candles": scalping_data.get("candles", []),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting market data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze")
async def force_analysis(request: dict):
    """Force analysis for symbol - Compatible with original trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")
        
        symbol = request.get("symbol")
        if not symbol:
            raise HTTPException(status_code=400, detail="Symbol required")
        
        # Use scalping engine's manual analysis
        result = await scalping_engine.manual_analyze_symbol(symbol)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "symbol": symbol,
            "analysis_result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error analyzing {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
