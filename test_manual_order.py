#!/usr/bin/env python3
"""
Manual order testing - Step by step
"""
import requests
import time

def test_step_by_step():
    """Test order placement step by step"""
    print("🎯 MANUAL ORDER TESTING - STEP BY STEP")
    print("=" * 60)
    
    # Step 1: Check system status
    print("\n📋 STEP 1: SYSTEM STATUS CHECK")
    try:
        response = requests.get("http://localhost:8001/api/trading/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ System Health: {status.get('system_health')}")
            print(f"✅ MT5 Connected: {status.get('is_mt5_connected')}")
            print(f"✅ Trading Enabled: {status.get('is_trading_enabled')}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Step 2: Start trading engine
    print("\n📋 STEP 2: START TRADING ENGINE")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Wait for initialization
    print("\n⏳ Waiting 3 seconds for initialization...")
    time.sleep(3)
    


    print("\n📋 STEP 3: ACCOUNT INFORMATION")
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        if response.status_code == 200:
            account = response.json()
            print(f"✅ Balance: ${account.get('balance', 0):.2f}")
            print(f"✅ Free Margin: ${account.get('free_margin', 0):.2f}")
            print(f"✅ Leverage: {account.get('leverage', 'Unknown')}")
        else:
            print(f"❌ Account check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Step 4: Test order placement
    print("\n📋 STEP 4: ORDER PLACEMENT TEST")
    print("⚠️  ATTEMPTING TO PLACE REAL ORDER!")
    print("📊 Order Details:")
    print("   - Symbol: BTCUSDm (cheapest)")
    print("   - Type: BUY")
    print("   - Lot Size: 0.01")
    print("   - Comment: Manual Test")
    
    try:
        response = requests.post("http://localhost:8001/api/trading/test-order", timeout=15)
        print(f"\n📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Response Details:")
            print(f"   Message: {result.get('message')}")
            print(f"   Ticket: {result.get('ticket')}")
            print(f"   Symbol: {result.get('symbol')}")
            
            if result.get('ticket'):
                print("\n🎉 SUCCESS! ORDER PLACED!")
                print("✅ Check your MT5 terminal for the order")
                
                # Check positions
                print("\n📋 STEP 5: VERIFY POSITION")
                pos_response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
                if pos_response.status_code == 200:
                    positions = pos_response.json()
                    print(f"✅ Total positions: {len(positions)}")
                    for pos in positions:
                        print(f"   📈 {pos.get('symbol')} - {pos.get('order_type')} - {pos.get('lot_size')} lots")
                
                return True
            else:
                print("\n❌ ORDER FAILED")
                print(f"   Error: {result.get('error')}")
                return False
        else:
            print(f"❌ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_autotrading_status():
    """Check if AutoTrading is enabled"""
    print("\n🔍 AUTOTRADING STATUS CHECK")
    print("=" * 40)
    print("If you see error 10027, follow these steps:")
    print("\n1. Open MT5 Terminal")
    print("2. Look for 'AutoTrading' button (traffic light icon)")
    print("3. Click to enable (should turn GREEN)")
    print("4. Or: Tools → Options → Expert Advisors → ✅ Allow automated trading")
    print("\n✅ When enabled, you should see:")
    print("   - Green AutoTrading button")
    print("   - 'AutoTrading is allowed' in status bar")

def monitor_backend():
    """Instructions for monitoring backend"""
    print("\n📋 BACKEND MONITORING")
    print("=" * 30)
    print("Watch your backend terminal for:")
    print("✅ '🧪 Testing order placement for BTCUSDm'")
    print("✅ 'Order details for BTCUSDm: lot_size=0.01...'")
    print("✅ 'Order placed successfully for BTCUSDm: Ticket XXXXX'")
    print("❌ 'Order failed for BTCUSDm: 10027 - AutoTrading disabled'")

if __name__ == "__main__":
    print("🎯 MANUAL ORDER PLACEMENT TEST")
    print("This will attempt to place a REAL order in your MT5 account")
    print("Make sure AutoTrading is enabled in MT5!")
    print("=" * 60)
    
    check_autotrading_status()
    monitor_backend()
    
    print("\n" + "=" * 60)
    input("Press ENTER to continue with order placement test...")
    
    success = test_step_by_step()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MANUAL ORDER TEST SUCCESSFUL!")
        print("✅ Order placed in MT5 account")
        print("✅ System working correctly")
        print("⚠️  Remember to manage your position in MT5")
    else:
        print("❌ MANUAL ORDER TEST FAILED")
        print("🔍 Check:")
        print("   - AutoTrading enabled in MT5")
        print("   - Backend logs for error details")
        print("   - MT5 terminal for error messages")
    
    print("\n🌐 Test completed - check MT5 terminal")
