#!/usr/bin/env python3
"""
Test script to verify scalping API endpoints work correctly
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/scalping/health")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health: {data}")
            return True
        else:
            print(f"❌ Health failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Health error: {e}")
        return False

def test_status():
    """Test status endpoint"""
    print("\n🔍 Testing status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/scalping/status")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {json.dumps(data, indent=2)}")
            return data
        else:
            print(f"❌ Status failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Status error: {e}")
        return None

def test_start_engine():
    """Test start engine endpoint"""
    print("\n🔍 Testing start engine endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/api/scalping/start")
        print(f"Status: {response.status_code}")
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if response.status_code == 200:
            if data.get("success") or "already running" in data.get("message", "").lower():
                print("✅ Start engine: Success or already running")
                return True
            else:
                print(f"⚠️ Start engine: {data.get('message', 'Unknown response')}")
                return False
        else:
            print(f"❌ Start engine failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Start engine error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Scalping API Endpoints\n")
    
    # Test health
    health_ok = test_health()
    if not health_ok:
        print("❌ Health check failed, stopping tests")
        return
    
    # Test status
    status_data = test_status()
    if not status_data:
        print("❌ Status check failed, stopping tests")
        return
    
    # Check if engine is running
    engine_running = status_data.get("data", {}).get("engine_running", False)
    print(f"\n🔄 Engine currently running: {engine_running}")
    
    # Test start engine
    start_ok = test_start_engine()
    
    # Final status check
    print("\n🔍 Final status check...")
    final_status = test_status()
    if final_status:
        final_engine_running = final_status.get("data", {}).get("engine_running", False)
        print(f"🔄 Engine running after test: {final_engine_running}")
    
    print("\n✅ API tests completed!")

if __name__ == "__main__":
    main()
