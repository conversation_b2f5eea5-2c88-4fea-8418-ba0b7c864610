#!/usr/bin/env python3
"""
Simple test untuk scalping system
Test basic functionality tanpa dependency kompleks
"""

import sys
import os
import asyncio
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

async def test_basic_imports():
    """Test basic imports"""
    print("1️⃣ TESTING BASIC IMPORTS")
    print("=" * 50)
    
    try:
        # Test MetaTrader5
        import MetaTrader5 as mt5
        print("   ✅ MetaTrader5: SUCCESS")
    except ImportError as e:
        print(f"   ❌ MetaTrader5: FAILED - {e}")
        return False
    
    try:
        # Test OpenAI
        from openai import AsyncOpenAI
        print("   ✅ OpenAI: SUCCESS")
    except ImportError as e:
        print(f"   ❌ OpenAI: FAILED - {e}")
        return False
    
    try:
        # Test FastAPI
        from fastapi import FastAPI
        print("   ✅ FastAPI: SUCCESS")
    except ImportError as e:
        print(f"   ❌ FastAPI: FAILED - {e}")
        return False
    
    try:
        # Test numpy/pandas
        import numpy as np
        import pandas as pd
        print("   ✅ NumPy/Pandas: SUCCESS")
    except ImportError as e:
        print(f"   ❌ NumPy/Pandas: FAILED - {e}")
        return False
    
    return True

async def test_mt5_connection():
    """Test MT5 connection"""
    print("\n2️⃣ TESTING MT5 CONNECTION")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        
        # Try to initialize MT5
        if not mt5.initialize():
            print("   ❌ MT5 Initialize: FAILED")
            print("   💡 Make sure MT5 is running")
            return False
        
        print("   ✅ MT5 Initialize: SUCCESS")
        
        # Try to get account info
        account_info = mt5.account_info()
        if account_info is None:
            print("   ❌ Account Info: FAILED")
            print("   💡 Check MT5 login credentials")
            mt5.shutdown()
            return False
        
        print(f"   ✅ Account Info: SUCCESS")
        print(f"      Account: {account_info.login}")
        print(f"      Balance: ${account_info.balance:.2f}")
        print(f"      Server: {account_info.server}")
        
        # Test symbol data
        symbol = "BTCUSDm"
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M15, 0, 10)
        
        if rates is None or len(rates) == 0:
            print(f"   ⚠️ Symbol Data ({symbol}): NO DATA")
            print("   💡 Try different symbol or check market hours")
        else:
            print(f"   ✅ Symbol Data ({symbol}): SUCCESS")
            print(f"      Candles: {len(rates)}")
            print(f"      Last Price: ${rates[-1]['close']:.2f}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"   ❌ MT5 Connection Error: {e}")
        return False

async def test_openai_connection():
    """Test OpenAI connection"""
    print("\n3️⃣ TESTING OPENAI CONNECTION")
    print("=" * 50)
    
    try:
        from openai import AsyncOpenAI
        
        # Get API key
        api_key = os.getenv("OPENAI_API_KEY", "")
        
        if not api_key:
            print("   ❌ OpenAI API Key: NOT FOUND")
            print("   💡 Set OPENAI_API_KEY in .env file")
            return False
        
        print(f"   ✅ OpenAI API Key: FOUND ({api_key[:10]}...)")
        
        # Test simple API call
        client = AsyncOpenAI(api_key=api_key)
        
        try:
            response = await client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": "Hello, respond with just 'OK'"}],
                max_tokens=10,
                temperature=0
            )
            
            if response and response.choices:
                print("   ✅ OpenAI API Call: SUCCESS")
                print(f"      Response: {response.choices[0].message.content}")
                return True
            else:
                print("   ❌ OpenAI API Call: NO RESPONSE")
                return False
                
        except Exception as api_error:
            print(f"   ❌ OpenAI API Call: FAILED - {api_error}")
            return False
        
    except Exception as e:
        print(f"   ❌ OpenAI Connection Error: {e}")
        return False

async def test_scalping_config():
    """Test scalping configuration"""
    print("\n4️⃣ TESTING SCALPING CONFIG")
    print("=" * 50)
    
    try:
        from backend.config.scalping_config import scalping_config
        
        print("   ✅ Scalping Config: SUCCESS")
        print(f"      Monitored Symbols: {scalping_config.monitored_symbols}")
        print(f"      Default Timeframe: {scalping_config.default_timeframe}")
        print(f"      Candles Count: {scalping_config.candles_count}")
        print(f"      Main Cycle: {scalping_config.main_cycle_interval}s")
        print(f"      GPT Model: {scalping_config.gpt_model}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Scalping Config Error: {e}")
        return False

async def test_data_fetcher():
    """Test data fetcher"""
    print("\n5️⃣ TESTING DATA FETCHER")
    print("=" * 50)
    
    try:
        from backend.services.data_fetcher import DataFetcher
        
        data_fetcher = DataFetcher()
        
        # Test connection
        connected = await data_fetcher.connect_mt5()
        if not connected:
            print("   ❌ Data Fetcher Connection: FAILED")
            return False
        
        print("   ✅ Data Fetcher Connection: SUCCESS")
        
        # Test data retrieval
        symbol = "BTCUSDm"
        scalping_data = await data_fetcher.get_scalping_data(symbol, "M15")
        
        if scalping_data:
            print(f"   ✅ Scalping Data ({symbol}): SUCCESS")
            print(f"      RSI: {scalping_data.get('rsi', 'N/A')}")
            print(f"      ATR: {scalping_data.get('atr', 'N/A')}")
            print(f"      Candles: {len(scalping_data.get('candles', []))}")
            print(f"      Data Quality: {scalping_data.get('data_quality', 'N/A')}")
        else:
            print(f"   ❌ Scalping Data ({symbol}): FAILED")
            return False
        
        await data_fetcher.disconnect_mt5()
        return True
        
    except Exception as e:
        print(f"   ❌ Data Fetcher Error: {e}")
        return False

async def run_simple_test():
    """Run simple scalping test"""
    print("🧪 SIMPLE SCALPING SYSTEM TEST")
    print("=" * 70)
    print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    test_results = []
    
    # Run tests
    tests = [
        ("Basic Imports", test_basic_imports),
        ("MT5 Connection", test_mt5_connection),
        ("OpenAI Connection", test_openai_connection),
        ("Scalping Config", test_scalping_config),
        ("Data Fetcher", test_data_fetcher)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} Test Failed: {e}")
            test_results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print("")
    print(f"📈 TOTAL TESTS: {len(test_results)}")
    print(f"✅ PASSED: {passed}")
    print(f"❌ FAILED: {failed}")
    print(f"📊 SUCCESS RATE: {(passed/len(test_results)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Scalping system is ready")
        print("\n🚀 NEXT STEPS:")
        print("   1. Run: python start_scalping_simple.py")
        print("   2. Check status: curl http://localhost:8001/api/scalping/status")
        print("   3. Monitor logs: tail -f logs/scalping_system.log")
    else:
        print(f"\n⚠️ {failed} TESTS FAILED")
        print("🔧 TROUBLESHOOTING:")
        if any("MT5" in name for name, result in test_results if not result):
            print("   - Make sure MT5 is running and logged in")
            print("   - Check MT5 credentials in .env file")
        if any("OpenAI" in name for name, result in test_results if not result):
            print("   - Set OPENAI_API_KEY in .env file")
            print("   - Check API key validity and credits")
        if any("Import" in name for name, result in test_results if not result):
            print("   - Install dependencies: pip install fastapi uvicorn openai MetaTrader5 numpy pandas")
    
    print("=" * 70)

if __name__ == "__main__":
    print("🎯 SIMPLE SCALPING SYSTEM TEST")
    print("Testing core functionality...")
    print("")
    
    try:
        asyncio.run(run_simple_test())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
