#!/usr/bin/env python3
"""
Test script to verify trading system compatibility endpoints work correctly
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def test_trading_endpoints():
    """Test trading system compatibility endpoints"""
    print("🚀 Testing Trading System Compatibility Endpoints\n")
    
    # Test health endpoint
    print("🔍 Testing /api/trading/account...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/account")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Account: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Account failed: {response.text}")
    except Exception as e:
        print(f"❌ Account error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test positions endpoint
    print("🔍 Testing /api/trading/positions...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/positions")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Positions: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Positions failed: {response.text}")
    except Exception as e:
        print(f"❌ Positions error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test status endpoint
    print("🔍 Testing /api/trading/status...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/status")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Status failed: {response.text}")
    except Exception as e:
        print(f"❌ Status error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test start endpoint
    print("🔍 Testing /api/trading/start...")
    try:
        response = requests.post(f"{BASE_URL}/api/trading/start", json={"auto_trading_enabled": True})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Start: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Start failed: {response.text}")
    except Exception as e:
        print(f"❌ Start error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test health endpoint
    print("🔍 Testing /api/health...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Health failed: {response.text}")
    except Exception as e:
        print(f"❌ Health error: {e}")

if __name__ == "__main__":
    test_trading_endpoints()
