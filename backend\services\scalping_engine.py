"""
MAIN SCALPING ENGINE
- Jalankan siklus: check_time_or_price_trigger() → data_fetcher → gpt_analyzer → order_manager
- Monitor buffer trigger via trigger_monitor
- Jalankan reevaluation saat harga mendekati
- <PERSON><PERSON><PERSON> setiap posisi yang ditutup
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from backend.services.data_fetcher import DataFetcher
from backend.services.scalping_gpt_analyzer import ScalpingGPTAnalyzer
from backend.services.trigger_monitor import TriggerMonitor
from backend.services.order_manager import OrderManager
from backend.services.price_monitor import PriceMonitor
from backend.services.reevaluation_service import ReevaluationService
from backend.services.evaluator_service import EvaluatorService

logger = logging.getLogger(__name__)

class ScalpingEngine:
    """
    Main scalping trading engine
    Coordinates all scalping components and manages the trading cycle
    """
    
    def __init__(self):
        # Initialize all services
        self.data_fetcher = DataFetcher()
        self.gpt_analyzer = ScalpingGPTAnalyzer()
        self.trigger_monitor = TriggerMonitor()
        self.order_manager = OrderManager()
        self.price_monitor = PriceMonitor()
        self.reevaluation_service = ReevaluationService()
        self.evaluator_service = EvaluatorService()

        # Engine state
        self.is_running = False
        self.is_initialized = False
        self.monitored_symbols = ["BTCUSDm", "ETHUSDm", "EURUSDm", "XAUUSDm"]

        # Account info (like trading engine)
        self.account_info = None

        # Cycle settings
        self.main_cycle_interval = 300  # 5 minutes
        self.reevaluation_interval = 180  # 3 minutes
        self.cleanup_interval = 3600  # 1 hour

        # Statistics
        self.stats = {
            "engine_started": None,
            "total_analyses": 0,
            "total_orders_placed": 0,
            "total_reevaluations": 0,
            "total_evaluations": 0,
            "last_cycle": None
        }

    async def initialize(self) -> bool:
        """Initialize the scalping engine - Following trading engine pattern"""
        try:
            logger.info("🔧 Initializing Scalping Engine...")

            # Connect to MT5
            if not await self.data_fetcher.connect_mt5():
                logger.error("❌ Failed to connect to MT5")
                return False

            # Get account info
            import MetaTrader5 as mt5
            account = mt5.account_info()
            if account:
                # Store account info in the same format as trading engine
                self.account_info = {
                    "login": account.login,
                    "balance": account.balance,
                    "equity": account.equity,
                    "margin": account.margin,
                    "free_margin": account.margin_free,
                    "margin_level": account.margin_level,
                    "profit": account.profit,
                    "currency": account.currency,
                    "leverage": account.leverage,
                    "server": account.server
                }
                logger.info(f"✅ Account info loaded: {account.login} - Balance: ${account.balance}")
            else:
                logger.error("❌ Failed to get account info")
                return False

            # Test GPT connection
            try:
                # Simple test to verify GPT is working
                test_data = {
                    "symbol": "TEST",
                    "timeframe": "M15",
                    "candles": [{"close": 100, "volume": 1000}],
                    "rsi": 50,
                    "atr": 1.0,
                    "volume_metrics": {"volume_increase_percent": 0}
                }
                # This will test if GPT client is properly initialized
                if hasattr(self.gpt_analyzer, 'client') and self.gpt_analyzer.client:
                    logger.info("✅ GPT connection verified")
                else:
                    logger.warning("⚠️ GPT connection not available")
            except Exception as e:
                logger.warning(f"⚠️ GPT connection test failed: {e}")

            self.is_initialized = True
            logger.info("✅ Scalping Engine initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize scalping engine: {e}")
            return False

    async def start_engine(self):
        """Start the scalping engine - Following trading engine pattern"""
        try:
            # Initialize first (like trading engine)
            if not await self.initialize():
                logger.error("❌ Cannot start scalping engine - initialization failed")
                return

            logger.info("🚀 Starting Scalping Engine...")

            self.is_running = True
            self.stats["engine_started"] = datetime.now().isoformat()

            # Start all monitoring tasks
            tasks = [
                asyncio.create_task(self._main_trading_cycle()),
                asyncio.create_task(self._reevaluation_cycle()),
                asyncio.create_task(self._cleanup_cycle()),
                asyncio.create_task(self.price_monitor.start_monitoring(self.monitored_symbols))
            ]

            logger.info(f"✅ Scalping Engine started for symbols: {self.monitored_symbols}")

            # Wait for all tasks
            await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(f"❌ Error starting scalping engine: {e}")
            self.is_running = False
    
    async def stop_engine(self):
        """Stop the scalping engine"""
        logger.info("🛑 Stopping Scalping Engine...")
        self.is_running = False
        
        # Cleanup any pending operations
        await self.order_manager.cleanup_expired_orders()
        
        logger.info("✅ Scalping Engine stopped")
    
    async def _main_trading_cycle(self):
        """Main trading cycle - check triggers and analyze"""
        logger.info("🔄 Starting main trading cycle...")
        
        while self.is_running:
            try:
                self.stats["last_cycle"] = datetime.now().isoformat()
                
                for symbol in self.monitored_symbols:
                    await self._process_symbol_cycle(symbol)
                
                # Wait before next cycle
                await asyncio.sleep(self.main_cycle_interval)
                
            except Exception as e:
                logger.error(f"❌ Error in main trading cycle: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _process_symbol_cycle(self, symbol: str):
        """Process trading cycle for a single symbol"""
        try:
            logger.debug(f"🔍 Processing cycle for {symbol}")
            
            # Check if trigger conditions are met
            trigger_status = await self.trigger_monitor.manual_trigger_check(symbol)
            
            if not trigger_status.get("trigger_ready", False):
                logger.debug(f"⏸️ No trigger for {symbol}: {trigger_status.get('movement_percent', 0):.2f}%")
                return
            
            logger.info(f"🚨 TRIGGER ACTIVATED for {symbol}")
            
            # Get scalping data
            scalping_data = await self.data_fetcher.get_scalping_data(symbol, "M15")
            
            if not scalping_data:
                logger.error(f"❌ Failed to get scalping data for {symbol}")
                return
            
            # Analyze with GPT
            gpt_analysis = await self.gpt_analyzer.analyze_scalping_opportunity(scalping_data)
            
            if not gpt_analysis:
                logger.error(f"❌ Failed to get GPT analysis for {symbol}")
                return
            
            self.stats["total_analyses"] += 1
            
            # Check if valid entry
            analysis_type = gpt_analysis.get("analisis", "")
            
            if analysis_type == "TIDAK ADA ENTRY VALID":
                logger.info(f"⏸️ No valid entry for {symbol}: {gpt_analysis.get('alasan', 'No reason')}")
                return
            
            # Place pending order
            order_id = await self.order_manager.place_pending_order(gpt_analysis, scalping_data)
            
            if order_id:
                self.stats["total_orders_placed"] += 1
                logger.info(f"✅ Order placed for {symbol}: {order_id}")
            else:
                logger.error(f"❌ Failed to place order for {symbol}")
            
        except Exception as e:
            logger.error(f"❌ Error processing symbol cycle for {symbol}: {e}")
    
    async def _reevaluation_cycle(self):
        """Re-evaluation cycle - check buffer zones and re-evaluate orders"""
        logger.info("🔄 Starting re-evaluation cycle...")
        
        while self.is_running:
            try:
                # Check and re-evaluate orders in buffer zones
                reevaluation_results = await self.reevaluation_service.check_and_reevaluate_orders()
                
                if reevaluation_results:
                    self.stats["total_reevaluations"] += len(reevaluation_results)
                    logger.info(f"🔄 Completed {len(reevaluation_results)} re-evaluations")
                
                # Wait before next re-evaluation cycle
                await asyncio.sleep(self.reevaluation_interval)
                
            except Exception as e:
                logger.error(f"❌ Error in re-evaluation cycle: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _cleanup_cycle(self):
        """Cleanup cycle - remove expired orders and old data"""
        logger.info("🧹 Starting cleanup cycle...")
        
        while self.is_running:
            try:
                # Cleanup expired orders
                await self.order_manager.cleanup_expired_orders()
                
                # Cleanup old re-evaluations
                await self.reevaluation_service.cleanup_old_reevaluations(days_to_keep=7)
                
                # Clear old price history
                self.price_monitor.clear_history()
                
                logger.info("🧹 Cleanup cycle completed")
                
                # Wait before next cleanup
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"❌ Error in cleanup cycle: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def manual_analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """Manually trigger analysis for a symbol"""
        try:
            logger.info(f"🔍 Manual analysis requested for {symbol}")
            
            # Get scalping data
            scalping_data = await self.data_fetcher.get_scalping_data(symbol, "M15")
            
            if not scalping_data:
                return {"error": f"Failed to get scalping data for {symbol}"}
            
            # Analyze with GPT
            gpt_analysis = await self.gpt_analyzer.analyze_scalping_opportunity(scalping_data)
            
            if not gpt_analysis:
                return {"error": f"Failed to get GPT analysis for {symbol}"}
            
            self.stats["total_analyses"] += 1
            
            return {
                "success": True,
                "symbol": symbol,
                "scalping_data": {
                    "rsi": scalping_data.get("rsi"),
                    "atr": scalping_data.get("atr"),
                    "volume_change": scalping_data.get("volume_metrics", {}).get("volume_increase_percent"),
                    "data_quality": scalping_data.get("data_quality")
                },
                "gpt_analysis": gpt_analysis,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error in manual analysis for {symbol}: {e}")
            return {"error": str(e)}
    
    async def manual_place_order(self, symbol: str) -> Dict[str, Any]:
        """Manually analyze and place order for symbol"""
        try:
            # First analyze
            analysis_result = await self.manual_analyze_symbol(symbol)
            
            if not analysis_result.get("success"):
                return analysis_result
            
            gpt_analysis = analysis_result["gpt_analysis"]
            
            # Check if valid entry
            if gpt_analysis.get("analisis") == "TIDAK ADA ENTRY VALID":
                return {
                    "success": False,
                    "reason": "No valid entry",
                    "gpt_reason": gpt_analysis.get("alasan", "No reason provided")
                }
            
            # Get fresh scalping data
            scalping_data = await self.data_fetcher.get_scalping_data(symbol, "M15")
            
            # Place order
            order_id = await self.order_manager.place_pending_order(gpt_analysis, scalping_data)
            
            if order_id:
                self.stats["total_orders_placed"] += 1
                return {
                    "success": True,
                    "order_id": order_id,
                    "analysis": gpt_analysis,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "reason": "Failed to place order",
                    "analysis": gpt_analysis
                }
                
        except Exception as e:
            logger.error(f"❌ Error in manual order placement for {symbol}: {e}")
            return {"error": str(e)}
    
    async def get_engine_status(self) -> Dict[str, Any]:
        """Get current engine status with MT5 and GPT information"""
        # Check MT5 connection status
        mt5_status = {
            "connected": False,
            "account_info": None,
            "error": None
        }

        try:
            mt5_connected = await self.data_fetcher.connect_mt5()
            if mt5_connected:
                import MetaTrader5 as mt5
                account_info = mt5.account_info()
                if account_info:
                    mt5_status["connected"] = True
                    mt5_status["account_info"] = {
                        "login": account_info.login,
                        "balance": account_info.balance,
                        "equity": account_info.equity,
                        "server": account_info.server,
                        "currency": account_info.currency
                    }
        except Exception as e:
            mt5_status["error"] = str(e)

        # Check GPT status
        gpt_status = {
            "available": False,
            "model": "gpt-4o",
            "api_key_configured": False,
            "error": None
        }

        try:
            # Check if GPT client is properly initialized
            if hasattr(self.gpt_analyzer, 'client') and self.gpt_analyzer.client:
                gpt_status["available"] = True
                gpt_status["api_key_configured"] = bool(self.gpt_analyzer.client.api_key)
        except Exception as e:
            gpt_status["error"] = str(e)

        # Get other status information
        active_orders = self.order_manager.get_active_orders()
        reevaluation_stats = self.reevaluation_service.get_reevaluation_statistics()
        price_monitor_status = self.price_monitor.get_monitoring_status()

        return {
            "engine_running": self.is_running,
            "monitored_symbols": self.monitored_symbols,
            "statistics": self.stats,
            "active_orders_count": active_orders["count"],
            "mt5_status": mt5_status,
            "gpt_status": gpt_status,
            "reevaluation_stats": reevaluation_stats,
            "price_monitor": {
                "monitored_symbols": price_monitor_status["monitored_symbols"],
                "last_significant_moves": price_monitor_status["last_significant_moves"]
            },
            "cycle_intervals": {
                "main_cycle_seconds": self.main_cycle_interval,
                "reevaluation_seconds": self.reevaluation_interval,
                "cleanup_seconds": self.cleanup_interval
            },
            "timestamp": datetime.now().isoformat()
        }
    
    async def evaluate_closed_trade(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate a closed trade"""
        try:
            evaluation_result = await self.evaluator_service.evaluate_closed_trade(trade_data)
            
            if evaluation_result:
                self.stats["total_evaluations"] += 1
                return {
                    "success": True,
                    "evaluation": evaluation_result,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to evaluate trade"
                }
                
        except Exception as e:
            logger.error(f"❌ Error evaluating closed trade: {e}")
            return {"error": str(e)}
