"""
Simple configuration for scalping system
"""
import os
from typing import List

class ScalpingConfig:
    """Configuration for scalping trading system"""
    
    def __init__(self):
        # OpenAI Configuration
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "")
        
        # MT5 Configuration
        self.mt5_login = os.getenv("MT5_LOGIN", "")
        self.mt5_password = os.getenv("MT5_PASSWORD", "")
        self.mt5_server = os.getenv("MT5_SERVER", "")
        
        # Scalping Settings
        self.monitored_symbols = ["BTCUSDm", "ETHUSDm", "EURUSD", "XAUUSD"]
        self.default_timeframe = "M15"
        self.candles_count = 30
        
        # Trigger Thresholds (percentage)
        self.pair_thresholds = {
            "BTCUSD": 0.30,
            "BTCUSDm": 0.30,
            "ETHUSD": 0.35,
            "ETHUSDm": 0.35,
            "EURUSDm": 0.15,
            "XAUUSDms": 0.25,
            "USDAED": 0.10
        }
        
        # Timing Settings (seconds)
        self.main_cycle_interval = 300      # 5 minutes
        self.reevaluation_interval = 180    # 3 minutes
        self.cleanup_interval = 3600        # 1 hour
        self.check_interval = 300           # 5 minutes
        
        # Cooldown Settings
        self.min_interval_minutes = 15      # 15 minutes cooldown
        
        # Buffer Settings
        self.default_buffer_multiplier = 0.5  # 0.5 × ATR
        
        # Order Settings
        self.default_lot_size = 0.01
        self.order_expiry_hours = 2
        self.magic_number = 234000
        
        # GPT Settings
        self.gpt_model = "gpt-4o"
        self.gpt_temperature = 0.2
        self.gpt_max_tokens = 500
        
        # Logging
        self.log_level = "INFO"
        self.log_file = "logs/scalping_system.log"
        
        # API Settings
        self.api_host = "0.0.0.0"
        self.api_port = 8001

# Global config instance
scalping_config = ScalpingConfig()
