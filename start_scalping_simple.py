#!/usr/bin/env python3
"""
Simple start script untuk SCALPING TRADING SYSTEM
Tidak bergantung pada backend.main yang kompleks
"""

import os
import sys
import asyncio
import uvicorn
import logging
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

def setup_logging():
    """Setup logging untuk scalping system"""
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/scalping_system.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def print_scalping_banner():
    """Print banner untuk scalping system"""
    print("=" * 70)
    print("🎯 SCALPING TRADING SYSTEM")
    print("=" * 70)
    print("📈 Auto Trading Scalping untuk Futures berbasis Python + GPT-4o")
    print("🧠 GPT sebagai Validator Struktur (bukan prediktor)")
    print("⚡ Pending Orders dengan Buffer & Revalidasi")
    print("📊 Sistem Logging dan Evaluator setelah posisi ditutup")
    print("")
    print("🔧 KOMPONEN AKTIF:")
    print("   📊 Data Fetcher - 30 candle M15 + RSI(14) + ATR(14)")
    print("   🧠 GPT Analyzer - Validator struktur candle + indikator")
    print("   🚨 Trigger Monitor - Pergerakan harga + cooldown timer")
    print("   📋 Order Manager - Pending orders + buffer zones")
    print("   💰 Price Monitor - Real-time tracking + moving window")
    print("   🔄 Reevaluation - Buffer zone analysis + order validation")
    print("   📈 Evaluator - Post-trade analysis + learning insights")
    print("")
    print("⚙️ PAIR THRESHOLDS:")
    print("   BTCUSDm: 0.30% movement triggers analysis")
    print("   ETHUSDm: 0.35% movement triggers analysis")
    print("   EURUSD: 0.15% movement triggers analysis")
    print("   XAUUSD: 0.25% movement triggers analysis")
    print("")
    print("🎯 SCALPING CRITERIA:")
    print("   BUY: Breakout bullish + RSI <40→>50 + Volume +20% + No upper wick")
    print("   SELL: Breakdown bearish + RSI >60→<50 + Volume +20% + No lower tail")
    print("")
    print("⏰ TIMING:")
    print("   Main Cycle: 5 minutes")
    print("   Re-evaluation: 3 minutes")
    print("   Cleanup: 1 hour")
    print("   Order Expiry: 2 hours")
    print("")
    print("📂 LOGS:")
    print("   System: logs/scalping_system.log")
    print("   Orders: logs/pending_orders.json")
    print("   Expired: logs/expired_orders.json")
    print("   Evaluations: logs/evaluations/")
    print("")
    print("🌐 API ENDPOINTS:")
    print("   Status: http://localhost:8001/api/scalping/status")
    print("   Analyze: POST http://localhost:8001/api/scalping/analyze/{symbol}")
    print("   Orders: http://localhost:8001/api/scalping/orders")
    print("   Health: http://localhost:8001/api/scalping/health")
    print("=" * 70)
    print("")

async def create_simple_scalping_app():
    """Create simple FastAPI app untuk scalping"""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    # Create FastAPI app
    app = FastAPI(
        title="Scalping Trading System",
        description="Auto Trading Scalping System with GPT-4o Analysis",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Import and include scalping routes
    try:
        from backend.api.scalping_routes import router as scalping_router, initialize_scalping_services
        
        # Initialize scalping services
        success = initialize_scalping_services()
        if success:
            print("✅ Scalping services initialized")
        else:
            print("⚠️ Some scalping services failed to initialize")
        
        # Include scalping router
        app.include_router(scalping_router)
        
    except Exception as e:
        print(f"⚠️ Error setting up scalping routes: {e}")
        print("📊 Basic API will still be available")
    
    # Basic health endpoint
    @app.get("/")
    async def root():
        return {
            "message": "Scalping Trading System API",
            "mode": "scalping",
            "version": "1.0.0",
            "status": "running"
        }
    
    @app.get("/health")
    async def health():
        return {"status": "healthy", "service": "scalping-system"}
    
    return app

async def start_scalping_engine():
    """Start scalping engine in background"""
    try:
        from backend.services.scalping_engine import ScalpingEngine
        
        scalping_engine = ScalpingEngine()
        print("🔄 Starting scalping engine...")
        
        # Start engine
        await scalping_engine.start_engine()
        
    except Exception as e:
        print(f"❌ Error starting scalping engine: {e}")
        print("📊 API server will still run without engine")

def main():
    """Main function untuk start scalping system"""
    print_scalping_banner()
    
    # Setup logging
    setup_logging()
    
    print("🚀 Starting Simple Scalping Trading System...")
    print("📊 API Server will be available at: http://localhost:8001")
    print("🔄 Scalping Engine will start automatically")
    print("📈 Monitoring symbols: BTCUSDm, ETHUSDm, EURUSD, XAUUSD")
    print("")
    print("💡 TIPS:")
    print("   - Check status: curl http://localhost:8001/api/scalping/status")
    print("   - Manual analysis: curl -X POST http://localhost:8001/api/scalping/analyze/BTCUSDm")
    print("   - View logs: tail -f logs/scalping_system.log")
    print("   - Stop system: Ctrl+C")
    print("")
    print("🔥 System starting in 3 seconds...")
    
    import time
    time.sleep(3)
    
    try:
        # Create app
        app = asyncio.run(create_simple_scalping_app())
        
        # Start scalping engine in background
        asyncio.create_task(start_scalping_engine())
        
        # Run uvicorn server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Scalping system stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting scalping system: {e}")
        print("\n🔧 TROUBLESHOOTING:")
        print("   1. Check if MT5 is running")
        print("   2. Verify OpenAI API key in .env file")
        print("   3. Install dependencies: pip install fastapi uvicorn openai MetaTrader5")
        print("   4. Check logs: tail -f logs/scalping_system.log")
        sys.exit(1)

if __name__ == "__main__":
    main()
