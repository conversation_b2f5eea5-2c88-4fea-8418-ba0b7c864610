"""
SCALPING GPT ANALYZER
- Kirim data ke GPT-4o (temperature: 0.2, max_tokens: 500)
- <PERSON><PERSON><PERSON> struktur candle dan indikator untuk scalping signals
- Val<PERSON><PERSON> struktur (bukan prediktor)
- Output: <PERSON><PERSON><PERSON> dengan pending order recommendations
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from openai import AsyncOpenAI
try:
    from backend.config.scalping_config import scalping_config as settings
except ImportError:
    # Fallback settings
    import os
    class Settings:
        openai_api_key = os.getenv("OPENAI_API_KEY", "")
    settings = Settings()

logger = logging.getLogger(__name__)

class ScalpingGPTAnalyzer:
    """
    GPT-4o Scalping Analyzer
    Specialized for scalping entry validation and pending order recommendations
    """
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = "gpt-4o"  # Use GPT-4o for scalping
        
    async def analyze_scalping_opportunity(
        self,
        scalping_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Analyze scalping opportunity using GPT-4o
        Returns: JSON with pending order recommendation or "TIDAK ADA ENTRY VALID"
        """
        try:
            logger.info(f"🧠 Analyzing scalping opportunity for {scalping_data.get('symbol', 'UNKNOWN')}")
            
            # Create scalping analysis prompt
            prompt = self._create_scalping_prompt(scalping_data)
            
            # Send to GPT-4o with scalping-optimized parameters
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_scalping_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,  # Low temperature for consistent analysis
                max_tokens=500    # Limited tokens for focused response
            )
            
            # Parse GPT response
            analysis_result = self._parse_scalping_response(
                response.choices[0].message.content,
                scalping_data
            )
            
            logger.info(f"✅ Scalping analysis completed for {scalping_data.get('symbol')}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Scalping GPT analysis error: {e}")
            return {
                "analisis": "TIDAK ADA ENTRY VALID",
                "alasan": f"Analysis error: {str(e)}",
                "timeframe": scalping_data.get('timeframe', 'M15'),
                "timestamp": datetime.now().isoformat()
            }
    
    def _get_scalping_system_prompt(self) -> str:
        """System prompt optimized for scalping analysis"""
        return """You are a professional scalping trader specializing in futures market analysis.

Your role is to VALIDATE candle structures and indicators for scalping opportunities, NOT to predict future prices.

SCALPING ENTRY CRITERIA:

BUY SIGNALS (ALL must be met):
1. Breakout candle bullish besar dengan close di atas resistance
2. RSI naik dari <40 ke atas 50
3. Volume naik 20% dari rata-rata
4. Tidak ada upper wick panjang (wick < 30% dari body)

SELL SIGNALS (ALL must be met):
1. Breakdown candle bearish besar dengan close di bawah support
2. RSI turun dari >60 ke bawah 50
3. Volume naik 20% dari rata-rata
4. Tidak ada lower tail panjang (tail < 30% dari body)

VALIDATION RULES:
- If NOT ALL criteria are met → "TIDAK ADA ENTRY VALID"
- Only recommend pending orders for CLEAR setups
- Focus on structure validation, not prediction
- Be conservative with entry recommendations

OUTPUT FORMAT (JSON only):
{
  "analisis": "BUY STOP / SELL LIMIT / BUY LIMIT / SELL STOP / TIDAK ADA ENTRY VALID",
  "entry_price": 108350,
  "stop_loss": 108150,
  "take_profit": 108750,
  "alasan": "Detailed explanation of why criteria are met/not met",
  "timeframe": "M15",
  "valid_until": "2025-01-01 14:30"
}

CRITICAL: Only recommend entry if ALL criteria are perfectly met. Be strict with validation."""

    def _create_scalping_prompt(self, scalping_data: Dict[str, Any]) -> str:
        """Create scalping-specific analysis prompt"""
        
        symbol = scalping_data.get('symbol', 'UNKNOWN')
        timeframe = scalping_data.get('timeframe', 'M15')
        rsi = scalping_data.get('rsi', 50)
        atr = scalping_data.get('atr', 0)
        volume_metrics = scalping_data.get('volume_metrics', {})
        last_candle = scalping_data.get('last_candle', {})
        candle_structure = scalping_data.get('candle_structure', {})
        
        # Get last 5 candles for context
        candles = scalping_data.get('candles', [])
        recent_candles = candles[-5:] if len(candles) >= 5 else candles
        
        prompt = f"""
SCALPING ANALYSIS REQUEST for {symbol} ({timeframe})

CURRENT MARKET DATA:
- Symbol: {symbol}
- Timeframe: {timeframe}
- Current RSI: {rsi}
- ATR: {atr}
- Last Candle: Open={last_candle.get('open', 0)}, High={last_candle.get('high', 0)}, Low={last_candle.get('low', 0)}, Close={last_candle.get('close', 0)}

VOLUME ANALYSIS:
- Last Volume: {volume_metrics.get('last_volume', 0)}
- Average Volume (3 candles): {volume_metrics.get('avg_volume_3', 0)}
- Volume Increase: {volume_metrics.get('volume_increase_percent', 0)}%

CANDLE STRUCTURE:
- Range: {candle_structure.get('range', 0)}
- Upper Wick: {candle_structure.get('upper_wick', 0)}
- Lower Wick: {candle_structure.get('lower_wick', 0)}
- Body Size: {candle_structure.get('body_size', 0)}
- Is Bullish: {candle_structure.get('is_bullish', False)}

RECENT CANDLES (Last 5):
"""
        
        # Add recent candles data
        for i, candle in enumerate(recent_candles):
            candle_type = "🟢" if candle['close'] > candle['open'] else "🔴"
            prompt += f"Candle {i+1} {candle_type}: O={candle['open']:.5f} H={candle['high']:.5f} L={candle['low']:.5f} C={candle['close']:.5f} V={candle['volume']}\n"
        
        prompt += f"""

VALIDATION CHECKLIST:

BUY CRITERIA:
□ Breakout candle bullish besar close di atas resistance?
□ RSI naik dari <40 ke atas 50? (Current: {rsi})
□ Volume naik 20%+ dari rata-rata? (Current: {volume_metrics.get('volume_increase_percent', 0)}%)
□ Tidak ada upper wick panjang?

SELL CRITERIA:
□ Breakdown candle bearish besar close di bawah support?
□ RSI turun dari >60 ke bawah 50? (Current: {rsi})
□ Volume naik 20%+ dari rata-rata? (Current: {volume_metrics.get('volume_increase_percent', 0)}%)
□ Tidak ada lower tail panjang?

TASK: Validate the structure and provide scalping recommendation.
If ALL criteria for BUY or SELL are met → recommend pending order
If NOT all criteria met → "TIDAK ADA ENTRY VALID"

Calculate entry price, stop loss (using ATR={atr}), and take profit levels.
Set valid_until to 2 hours from now.

Respond with JSON format only."""
        
        return prompt
    
    def _parse_scalping_response(
        self,
        gpt_response: str,
        scalping_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse GPT response for scalping analysis"""
        try:
            # Try to extract JSON from response
            response_text = gpt_response.strip()
            
            # Find JSON in response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_text = response_text[start_idx:end_idx]
                analysis_result = json.loads(json_text)
                
                # Validate required fields
                required_fields = ['analisis', 'alasan', 'timeframe']
                for field in required_fields:
                    if field not in analysis_result:
                        analysis_result[field] = "N/A"
                
                # Add timestamp if not present
                if 'timestamp' not in analysis_result:
                    analysis_result['timestamp'] = datetime.now().isoformat()
                
                # Add symbol
                analysis_result['symbol'] = scalping_data.get('symbol', 'UNKNOWN')
                
                # Set valid_until if not present
                if 'valid_until' not in analysis_result:
                    valid_until = datetime.now() + timedelta(hours=2)
                    analysis_result['valid_until'] = valid_until.strftime('%Y-%m-%d %H:%M')
                
                logger.info(f"📊 Scalping analysis: {analysis_result.get('analisis', 'UNKNOWN')}")
                return analysis_result
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            logger.error(f"❌ Error parsing scalping response: {e}")
            logger.debug(f"Raw response: {gpt_response}")
            
            # Return safe default
            return {
                "analisis": "TIDAK ADA ENTRY VALID",
                "alasan": f"Failed to parse GPT response: {str(e)}",
                "timeframe": scalping_data.get('timeframe', 'M15'),
                "symbol": scalping_data.get('symbol', 'UNKNOWN'),
                "timestamp": datetime.now().isoformat(),
                "valid_until": (datetime.now() + timedelta(hours=2)).strftime('%Y-%m-%d %H:%M')
            }
    
    async def reevaluate_pending_order(
        self,
        order_data: Dict[str, Any],
        current_scalping_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Re-evaluate pending order when price approaches buffer zone
        """
        try:
            logger.info(f"🔄 Re-evaluating pending order for {order_data.get('symbol', 'UNKNOWN')}")
            
            prompt = f"""
PENDING ORDER RE-EVALUATION

ORIGINAL ORDER:
- Type: {order_data.get('analisis', 'UNKNOWN')}
- Entry Price: {order_data.get('entry_price', 0)}
- Symbol: {order_data.get('symbol', 'UNKNOWN')}
- Created: {order_data.get('timestamp', 'UNKNOWN')}

CURRENT MARKET STRUCTURE:
- Current Price: {current_scalping_data.get('last_candle', {}).get('close', 0)}
- RSI: {current_scalping_data.get('rsi', 50)}
- Volume Change: {current_scalping_data.get('volume_metrics', {}).get('volume_increase_percent', 0)}%

Harga mendekati area pending order. Apakah struktur saat ini masih valid untuk mengeksekusi order ini?

Analyze current market structure and determine if the original setup is still valid.

Respond with JSON:
{
  "keputusan": "LANJUTKAN ORDER / CANCEL ORDER",
  "alasan": "Detailed explanation of why to continue or cancel"
}
"""
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are a scalping expert re-evaluating pending orders. Be conservative - cancel if structure has changed."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,
                max_tokens=300
            )
            
            # Parse re-evaluation response
            response_text = response.choices[0].message.content.strip()
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_text = response_text[start_idx:end_idx]
                result = json.loads(json_text)
                result['timestamp'] = datetime.now().isoformat()
                return result
            else:
                return {
                    "keputusan": "CANCEL ORDER",
                    "alasan": "Failed to parse re-evaluation response",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error in re-evaluation: {e}")
            return {
                "keputusan": "CANCEL ORDER",
                "alasan": f"Re-evaluation error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
