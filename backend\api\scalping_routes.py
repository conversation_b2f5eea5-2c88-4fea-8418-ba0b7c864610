"""
SCALPING API ROUTES
API endpoints untuk scalping trading system
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

# Import scalping services
from backend.services.scalping_engine import ScalpingEngine
from backend.services.data_fetcher import DataFetcher
from backend.services.scalping_gpt_analyzer import ScalpingGPTAnalyzer
from backend.services.trigger_monitor import TriggerMonitor
from backend.services.order_manager import OrderManager
from backend.services.price_monitor import PriceMonitor
from backend.services.reevaluation_service import ReevaluationService
from backend.services.evaluator_service import EvaluatorService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/scalping", tags=["scalping"])

# Create compatibility router without prefix for /api/trading
compat_router = APIRouter(tags=["trading-compat"])

# Global instances (will be initialized by main app)
scalping_engine: Optional[ScalpingEngine] = None
data_fetcher: Optional[DataFetcher] = None
gpt_analyzer: Optional[ScalpingGPTAnalyzer] = None
trigger_monitor: Optional[TriggerMonitor] = None
order_manager: Optional[OrderManager] = None
price_monitor: Optional[PriceMonitor] = None
reevaluation_service: Optional[ReevaluationService] = None
evaluator_service: Optional[EvaluatorService] = None

def initialize_scalping_services():
    """Initialize all scalping services"""
    global data_fetcher, gpt_analyzer, trigger_monitor
    global order_manager, price_monitor, reevaluation_service, evaluator_service

    try:
        # Don't create scalping_engine here - it's created in main.py
        data_fetcher = DataFetcher()
        gpt_analyzer = ScalpingGPTAnalyzer()
        trigger_monitor = TriggerMonitor()
        order_manager = OrderManager()
        price_monitor = PriceMonitor()
        reevaluation_service = ReevaluationService()
        evaluator_service = EvaluatorService()

        logger.info("✅ Scalping services initialized")
        return True
    except Exception as e:
        logger.error(f"❌ Error initializing scalping services: {e}")
        return False

def set_scalping_engine(engine):
    """Set the scalping engine instance from main.py"""
    global scalping_engine
    scalping_engine = engine

# ============================================================================
# ENGINE STATUS & CONTROL
# ============================================================================

@router.get("/status")
async def get_scalping_status():
    """Get scalping engine status"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")

        status = await scalping_engine.get_engine_status()
        return {"success": True, "data": status}

    except Exception as e:
        logger.error(f"❌ Error getting scalping status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# COMPATIBILITY ENDPOINTS FOR DASHBOARD
# ============================================================================

@router.get("/account")
async def get_account_info():
    """Get account information - Compatible with trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Data fetcher not initialized")

        # Connect to MT5 and get account info
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            raise HTTPException(status_code=503, detail="MT5 not connected")

        import MetaTrader5 as mt5
        account_info = mt5.account_info()
        if not account_info:
            raise HTTPException(status_code=503, detail="Failed to get account info")

        # Return in the same format as trading system
        return {
            "login": account_info.login,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "free_margin": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "profit": account_info.profit,
            "currency": account_info.currency,
            "leverage": account_info.leverage,
            "server": account_info.server
        }

    except Exception as e:
        logger.error(f"❌ Error getting account info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/positions")
async def get_positions():
    """Get all positions - Compatible with trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Data fetcher not initialized")

        # Connect to MT5 and get positions
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            return []  # Return empty list if not connected

        import MetaTrader5 as mt5
        positions = mt5.positions_get()
        if not positions:
            return []

        position_list = []
        for pos in positions:
            # Get current price for P&L calculation
            tick = mt5.symbol_info_tick(pos.symbol)
            current_price = tick.bid if pos.type == 0 else tick.ask if tick else pos.price_current

            # Calculate profit
            profit = pos.profit + pos.swap + getattr(pos, 'commission', 0.0)

            position_list.append({
                "ticket": pos.ticket,
                "symbol": pos.symbol,
                "order_type": "BUY" if pos.type == 0 else "SELL",
                "lot_size": pos.volume,
                "entry_price": pos.price_open,
                "current_price": current_price,
                "stop_loss": pos.sl if pos.sl != 0 else None,
                "take_profit": pos.tp if pos.tp != 0 else None,
                "open_time": datetime.fromtimestamp(pos.time).isoformat(),
                "status": "OPEN",
                "profit": profit,
                "commission": getattr(pos, 'commission', 0.0),
                "swap": getattr(pos, 'swap', 0.0),
                "comment": getattr(pos, 'comment', '')
            })

        return position_list

    except Exception as e:
        logger.error(f"❌ Error getting positions: {e}")
        return []  # Return empty list on error



@router.post("/start")
async def start_scalping_engine(request: dict = None):
    """Start scalping engine - Compatible with both scalping and trading endpoints"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")

        if scalping_engine.is_running:
            # For compatibility with frontend, return success format like trading system
            return {"message": "Engine already running", "status": "running"}

        # Start engine in background
        import asyncio
        asyncio.create_task(scalping_engine.start_engine())

        return {"message": "Scalping engine started successfully", "status": "running"}

    except Exception as e:
        logger.error(f"❌ Error starting scalping engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop")
async def stop_scalping_engine():
    """Stop scalping engine - Compatible with both scalping and trading endpoints"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")

        if not scalping_engine.is_running:
            # For compatibility with frontend, return success format like trading system
            return {"message": "Engine already stopped", "status": "stopped"}

        await scalping_engine.stop_engine()
        return {"message": "Scalping engine stopped successfully", "status": "stopped"}

    except Exception as e:
        logger.error(f"❌ Error stopping scalping engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# MARKET DATA & ANALYSIS
# ============================================================================

@router.get("/data/{symbol}")
async def get_scalping_data(symbol: str):
    """Get scalping data for symbol"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Data fetcher not initialized")
        
        scalping_data = await data_fetcher.get_scalping_data(symbol, "M15")
        
        if not scalping_data:
            raise HTTPException(status_code=404, detail=f"No data available for {symbol}")
        
        return {"success": True, "data": scalping_data}
        
    except Exception as e:
        logger.error(f"❌ Error getting scalping data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze/{symbol}")
async def analyze_symbol(symbol: str):
    """Manually analyze symbol for scalping opportunity"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")
        
        result = await scalping_engine.manual_analyze_symbol(symbol)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {"success": True, "data": result}
        
    except Exception as e:
        logger.error(f"❌ Error analyzing {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/place-order/{symbol}")
async def place_order(symbol: str):
    """Manually analyze and place order for symbol"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")
        
        result = await scalping_engine.manual_place_order(symbol)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        if not result.get("success"):
            return {"success": False, "data": result}
        
        return {"success": True, "data": result}
        
    except Exception as e:
        logger.error(f"❌ Error placing order for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# ORDER MANAGEMENT
# ============================================================================

@router.get("/orders")
async def get_active_orders():
    """Get all active pending orders"""
    try:
        if not order_manager:
            raise HTTPException(status_code=503, detail="Order manager not initialized")
        
        orders = order_manager.get_active_orders()
        return {"success": True, "data": orders}
        
    except Exception as e:
        logger.error(f"❌ Error getting active orders: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/orders/{order_id}")
async def cancel_order(order_id: str, reason: str = "Manual cancel"):
    """Cancel pending order"""
    try:
        if not order_manager:
            raise HTTPException(status_code=503, detail="Order manager not initialized")
        
        success = await order_manager.cancel_order(order_id, reason)
        
        if success:
            return {"success": True, "message": f"Order {order_id} cancelled"}
        else:
            raise HTTPException(status_code=404, detail=f"Order {order_id} not found or failed to cancel")
        
    except Exception as e:
        logger.error(f"❌ Error cancelling order {order_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# TRIGGER & PRICE MONITORING
# ============================================================================

@router.get("/triggers/{symbol}")
async def get_trigger_status(symbol: str):
    """Get trigger status for symbol"""
    try:
        if not trigger_monitor:
            raise HTTPException(status_code=503, detail="Trigger monitor not initialized")
        
        status = await trigger_monitor.manual_trigger_check(symbol)
        
        if "error" in status:
            raise HTTPException(status_code=400, detail=status["error"])
        
        return {"success": True, "data": status}
        
    except Exception as e:
        logger.error(f"❌ Error getting trigger status for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/price/{symbol}")
async def get_price_analysis(symbol: str):
    """Get price movement analysis for symbol"""
    try:
        if not price_monitor:
            raise HTTPException(status_code=503, detail="Price monitor not initialized")
        
        analysis = await price_monitor.get_price_movement_analysis(symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=400, detail=analysis["error"])
        
        return {"success": True, "data": analysis}
        
    except Exception as e:
        logger.error(f"❌ Error getting price analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/monitoring-status")
async def get_monitoring_status():
    """Get overall monitoring status"""
    try:
        trigger_status = trigger_monitor.get_monitoring_status() if trigger_monitor else {}
        price_status = price_monitor.get_monitoring_status() if price_monitor else {}
        
        return {
            "success": True,
            "data": {
                "trigger_monitor": trigger_status,
                "price_monitor": price_status,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# REEVALUATION & EVALUATION
# ============================================================================

@router.post("/reevaluate")
async def reevaluate_orders():
    """Manually trigger re-evaluation of all orders"""
    try:
        if not reevaluation_service:
            raise HTTPException(status_code=503, detail="Reevaluation service not initialized")
        
        results = await reevaluation_service.check_and_reevaluate_orders()
        
        return {
            "success": True,
            "data": {
                "reevaluations_performed": len(results),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Error in manual re-evaluation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reevaluate/{order_id}")
async def reevaluate_order(order_id: str):
    """Manually re-evaluate specific order"""
    try:
        if not reevaluation_service:
            raise HTTPException(status_code=503, detail="Reevaluation service not initialized")
        
        result = await reevaluation_service.manual_reevaluate_order(order_id)
        
        return {"success": True, "data": result}
        
    except Exception as e:
        logger.error(f"❌ Error re-evaluating order {order_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/evaluations")
async def get_evaluations(symbol: Optional[str] = None):
    """Get trade evaluations"""
    try:
        if not evaluator_service:
            raise HTTPException(status_code=503, detail="Evaluator service not initialized")
        
        summary = evaluator_service.get_evaluation_summary(symbol)
        
        if "error" in summary:
            raise HTTPException(status_code=400, detail=summary["error"])
        
        return {"success": True, "data": summary}
        
    except Exception as e:
        logger.error(f"❌ Error getting evaluations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/evaluate-trade")
async def evaluate_trade(trade_data: Dict[str, Any]):
    """Manually evaluate a closed trade"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Scalping engine not initialized")
        
        result = await scalping_engine.evaluate_closed_trade(trade_data)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {"success": True, "data": result}
        
    except Exception as e:
        logger.error(f"❌ Error evaluating trade: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# SYSTEM UTILITIES
# ============================================================================

@router.post("/cleanup")
async def cleanup_system():
    """Manually trigger system cleanup"""
    try:
        cleanup_results = {}
        
        # Cleanup expired orders
        if order_manager:
            await order_manager.cleanup_expired_orders()
            cleanup_results["expired_orders"] = "cleaned"
        
        # Cleanup old re-evaluations
        if reevaluation_service:
            await reevaluation_service.cleanup_old_reevaluations(days_to_keep=7)
            cleanup_results["old_reevaluations"] = "cleaned"
        
        # Clear price history
        if price_monitor:
            price_monitor.clear_history()
            cleanup_results["price_history"] = "cleared"
        
        return {
            "success": True,
            "data": {
                "cleanup_results": cleanup_results,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Error in system cleanup: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """Health check endpoint with MT5 and GPT validation"""
    try:
        # Test MT5 connection
        mt5_connected = False
        mt5_account_info = None
        if data_fetcher:
            try:
                mt5_connected = await data_fetcher.connect_mt5()
                if mt5_connected:
                    # Get account info to verify connection
                    import MetaTrader5 as mt5
                    account_info = mt5.account_info()
                    if account_info:
                        mt5_account_info = {
                            "login": account_info.login,
                            "balance": account_info.balance,
                            "server": account_info.server
                        }
            except Exception as e:
                logger.warning(f"MT5 health check failed: {e}")
                mt5_connected = False

        # Test GPT connection
        gpt_available = False
        if gpt_analyzer:
            try:
                # Simple test to verify GPT is working
                test_data = {
                    "symbol": "TEST",
                    "timeframe": "M15",
                    "candles": [{"close": 100, "volume": 1000}],
                    "rsi": 50,
                    "atr": 1.0,
                    "volume_metrics": {"volume_increase_percent": 0}
                }
                # This will test if GPT client is properly initialized
                if hasattr(gpt_analyzer, 'client') and gpt_analyzer.client:
                    gpt_available = True
            except Exception as e:
                logger.warning(f"GPT health check failed: {e}")
                gpt_available = False

        services_status = {
            "scalping_engine": scalping_engine is not None and scalping_engine.is_running,
            "data_fetcher": data_fetcher is not None,
            "gpt_analyzer": gpt_analyzer is not None,
            "trigger_monitor": trigger_monitor is not None,
            "order_manager": order_manager is not None,
            "price_monitor": price_monitor is not None,
            "reevaluation_service": reevaluation_service is not None,
            "evaluator_service": evaluator_service is not None,
            "mt5_connected": mt5_connected,
            "gpt_available": gpt_available
        }

        # Determine overall health status
        critical_services = ["data_fetcher", "gpt_analyzer", "mt5_connected", "gpt_available"]
        critical_healthy = all(services_status.get(service, False) for service in critical_services)
        all_healthy = all(services_status.values())

        if critical_healthy and all_healthy:
            status = "healthy"
        elif critical_healthy:
            status = "degraded"
        else:
            status = "unhealthy"

        response_data = {
            "status": status,
            "services": services_status,
            "timestamp": datetime.now().isoformat()
        }

        # Add MT5 account info if available
        if mt5_account_info:
            response_data["mt5_account"] = mt5_account_info

        return {
            "success": True,
            "data": response_data
        }

    except Exception as e:
        logger.error(f"❌ Error in health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# COMPATIBILITY ROUTER FOR /api/trading ENDPOINTS
# ============================================================================

@compat_router.get("/status")
async def get_trading_status():
    """Get system status - Compatible with trading system format"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        # Use account info from scalping engine (like trading engine)
        account_info = scalping_engine.account_info

        # Return in trading system format
        return {
            "is_running": scalping_engine.is_running,
            "auto_trading_enabled": True,  # Scalping is always auto
            "monitored_symbols": scalping_engine.monitored_symbols,
            "daily_profit": account_info.get("profit", 0.0) if account_info else 0.0,
            "last_analysis_time": datetime.now().isoformat(),
            "account_balance": account_info.get("balance", 0.0) if account_info else 0.0,
            "account_equity": account_info.get("equity", 0.0) if account_info else 0.0,
            "system_type": "scalping"
        }

    except Exception as e:
        logger.error(f"❌ Error getting trading status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@compat_router.get("/account")
async def get_trading_account_info():
    """Get account information - Compatible with trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        # Use account info from scalping engine (like trading engine)
        if not scalping_engine.account_info:
            # If not available, try to get it fresh
            if not scalping_engine.is_initialized:
                raise HTTPException(status_code=503, detail="Trading engine not initialized - please start the engine first")

            # Try to refresh account info
            import MetaTrader5 as mt5
            account = mt5.account_info()
            if account:
                scalping_engine.account_info = {
                    "login": account.login,
                    "balance": account.balance,
                    "equity": account.equity,
                    "margin": account.margin,
                    "free_margin": account.margin_free,
                    "margin_level": account.margin_level,
                    "profit": account.profit,
                    "currency": account.currency,
                    "leverage": account.leverage,
                    "server": account.server
                }
            else:
                raise HTTPException(status_code=503, detail="Account information not available - MT5 not connected or engine not initialized")

        return scalping_engine.account_info

    except Exception as e:
        logger.error(f"❌ Error getting account info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@compat_router.get("/positions")
async def get_trading_positions():
    """Get all positions - Compatible with trading system"""
    try:
        if not data_fetcher:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        # Connect to MT5 and get positions
        mt5_connected = await data_fetcher.connect_mt5()
        if not mt5_connected:
            return []  # Return empty list if not connected

        import MetaTrader5 as mt5
        positions = mt5.positions_get()
        if not positions:
            return []

        position_list = []
        for pos in positions:
            # Get current price for P&L calculation
            tick = mt5.symbol_info_tick(pos.symbol)
            current_price = tick.bid if pos.type == 0 else tick.ask if tick else pos.price_current

            # Calculate profit
            profit = pos.profit + pos.swap + getattr(pos, 'commission', 0.0)

            position_list.append({
                "ticket": pos.ticket,
                "symbol": pos.symbol,
                "order_type": "BUY" if pos.type == 0 else "SELL",
                "lot_size": pos.volume,
                "entry_price": pos.price_open,
                "current_price": current_price,
                "stop_loss": pos.sl if pos.sl != 0 else None,
                "take_profit": pos.tp if pos.tp != 0 else None,
                "open_time": datetime.fromtimestamp(pos.time).isoformat(),
                "status": "OPEN",
                "profit": profit,
                "commission": getattr(pos, 'commission', 0.0),
                "swap": getattr(pos, 'swap', 0.0),
                "comment": getattr(pos, 'comment', '')
            })

        return position_list

    except Exception as e:
        logger.error(f"❌ Error getting positions: {e}")
        return []  # Return empty list on error

@compat_router.post("/start")
async def start_trading_engine(request: dict = None):
    """Start trading engine - Compatible with trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        if scalping_engine.is_running:
            return {"message": "Trading engine started successfully", "status": "running"}

        # Start engine in background
        import asyncio
        asyncio.create_task(scalping_engine.start_engine())

        return {"message": "Trading engine started successfully", "status": "running"}

    except Exception as e:
        logger.error(f"❌ Error starting trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@compat_router.post("/stop")
async def stop_trading_engine():
    """Stop trading engine - Compatible with trading system"""
    try:
        if not scalping_engine:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        if not scalping_engine.is_running:
            return {"message": "Trading engine stopped successfully", "status": "stopped"}

        await scalping_engine.stop_engine()
        return {"message": "Trading engine stopped successfully", "status": "stopped"}

    except Exception as e:
        logger.error(f"❌ Error stopping trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))
