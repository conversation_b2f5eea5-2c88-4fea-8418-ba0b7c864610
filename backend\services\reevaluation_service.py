"""
REEVALUATION SERVICE
- Jika harga masuk buffer zone: ambil ulang data dan kirim ke GPT
- Prompt: "Harga mendekati area pending order, apakah struktur masih valid?"
- Output: "LANJUTKAN ORDER / CANCEL ORDER"
- Jika CANCEL ORDER → hapus pending dari MT5 + log di expired_orders.json
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from backend.services.data_fetcher import DataFetcher
from backend.services.scalping_gpt_analyzer import ScalpingGPTAnalyzer
from backend.services.order_manager import OrderManager

logger = logging.getLogger(__name__)

class ReevaluationService:
    """
    Re-evaluate pending orders when price approaches buffer zones
    """
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.gpt_analyzer = ScalpingGPTAnalyzer()
        self.order_manager = OrderManager()
        
        # Reevaluation tracking
        self.reevaluation_history = {}  # order_id -> [reevaluation_data, ...]
        
    async def check_and_reevaluate_orders(self) -> List[Dict[str, Any]]:
        """
        Check all active orders and re-evaluate those in buffer zones
        Returns list of reevaluation results
        """
        try:
            logger.info("🔄 Checking orders for re-evaluation...")
            
            # Get orders that need re-evaluation
            orders_to_reevaluate = await self.order_manager.check_buffer_zones()
            
            if not orders_to_reevaluate:
                logger.debug("✅ No orders need re-evaluation")
                return []
            
            reevaluation_results = []
            
            for order_id in orders_to_reevaluate:
                result = await self._reevaluate_single_order(order_id)
                if result:
                    reevaluation_results.append(result)
            
            logger.info(f"🔄 Completed re-evaluation of {len(reevaluation_results)} orders")
            return reevaluation_results
            
        except Exception as e:
            logger.error(f"❌ Error in check_and_reevaluate_orders: {e}")
            return []
    
    async def _reevaluate_single_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Re-evaluate a single pending order"""
        try:
            logger.info(f"🔄 Re-evaluating order: {order_id}")
            
            # Get order data
            active_orders = self.order_manager.get_active_orders()
            order_data = active_orders["active_orders"].get(order_id)
            
            if not order_data:
                logger.warning(f"⚠️ Order {order_id} not found in active orders")
                return None
            
            symbol = order_data["symbol"]
            
            # Get fresh scalping data
            current_scalping_data = await self.data_fetcher.get_scalping_data(symbol, "M15")
            
            if not current_scalping_data:
                logger.error(f"❌ Failed to get current scalping data for {symbol}")
                return None
            
            # Perform GPT re-evaluation
            reevaluation_result = await self.gpt_analyzer.reevaluate_pending_order(
                order_data, 
                current_scalping_data
            )
            
            if not reevaluation_result:
                logger.error(f"❌ Failed to get GPT re-evaluation for {order_id}")
                return None
            
            # Process re-evaluation decision
            decision = reevaluation_result.get("keputusan", "CANCEL ORDER")
            reason = reevaluation_result.get("alasan", "No reason provided")
            
            logger.info(f"🧠 GPT Re-evaluation for {order_id}: {decision}")
            logger.info(f"   💭 Reason: {reason}")
            
            # Create reevaluation record
            reevaluation_record = {
                "order_id": order_id,
                "symbol": symbol,
                "decision": decision,
                "reason": reason,
                "current_price": current_scalping_data.get("last_candle", {}).get("close", 0),
                "original_entry": order_data.get("entry_price", 0),
                "buffer_zone": order_data.get("buffer_zone", {}),
                "current_rsi": current_scalping_data.get("rsi", 50),
                "current_volume_change": current_scalping_data.get("volume_metrics", {}).get("volume_increase_percent", 0),
                "reevaluation_timestamp": datetime.now().isoformat(),
                "gpt_response": reevaluation_result
            }
            
            # Store reevaluation history
            if order_id not in self.reevaluation_history:
                self.reevaluation_history[order_id] = []
            self.reevaluation_history[order_id].append(reevaluation_record)
            
            # Execute decision
            if decision == "CANCEL ORDER":
                success = await self.order_manager.cancel_order(
                    order_id, 
                    f"GPT Re-evaluation: {reason}"
                )
                
                if success:
                    logger.info(f"✅ Order {order_id} cancelled based on GPT re-evaluation")
                    reevaluation_record["action_taken"] = "ORDER_CANCELLED"
                else:
                    logger.error(f"❌ Failed to cancel order {order_id}")
                    reevaluation_record["action_taken"] = "CANCEL_FAILED"
            
            elif decision == "LANJUTKAN ORDER":
                logger.info(f"✅ Order {order_id} continues - structure still valid")
                reevaluation_record["action_taken"] = "ORDER_CONTINUED"
            
            else:
                logger.warning(f"⚠️ Unknown decision for {order_id}: {decision}")
                reevaluation_record["action_taken"] = "UNKNOWN_DECISION"
            
            return reevaluation_record
            
        except Exception as e:
            logger.error(f"❌ Error re-evaluating order {order_id}: {e}")
            return {
                "order_id": order_id,
                "decision": "CANCEL ORDER",
                "reason": f"Re-evaluation error: {str(e)}",
                "action_taken": "ERROR_CANCEL",
                "reevaluation_timestamp": datetime.now().isoformat()
            }
    
    async def manual_reevaluate_order(self, order_id: str) -> Dict[str, Any]:
        """Manually trigger re-evaluation for specific order"""
        try:
            logger.info(f"🔄 Manual re-evaluation requested for {order_id}")
            
            result = await self._reevaluate_single_order(order_id)
            
            if result:
                return {
                    "success": True,
                    "reevaluation_result": result,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to re-evaluate order",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error in manual re-evaluation for {order_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_reevaluation_history(self, order_id: Optional[str] = None) -> Dict[str, Any]:
        """Get re-evaluation history for specific order or all orders"""
        try:
            if order_id:
                history = self.reevaluation_history.get(order_id, [])
                return {
                    "order_id": order_id,
                    "reevaluations": history,
                    "count": len(history),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                total_reevaluations = sum(len(history) for history in self.reevaluation_history.values())
                return {
                    "all_orders": self.reevaluation_history,
                    "total_orders_reevaluated": len(self.reevaluation_history),
                    "total_reevaluations": total_reevaluations,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting re-evaluation history: {e}")
            return {"error": str(e)}
    
    def get_reevaluation_statistics(self) -> Dict[str, Any]:
        """Get statistics about re-evaluations"""
        try:
            total_reevaluations = 0
            decisions_count = {"LANJUTKAN ORDER": 0, "CANCEL ORDER": 0, "UNKNOWN": 0}
            actions_count = {"ORDER_CANCELLED": 0, "ORDER_CONTINUED": 0, "CANCEL_FAILED": 0, "ERROR_CANCEL": 0}
            
            for order_id, history in self.reevaluation_history.items():
                total_reevaluations += len(history)
                
                for reevaluation in history:
                    decision = reevaluation.get("decision", "UNKNOWN")
                    action = reevaluation.get("action_taken", "UNKNOWN")
                    
                    if decision in decisions_count:
                        decisions_count[decision] += 1
                    else:
                        decisions_count["UNKNOWN"] += 1
                    
                    if action in actions_count:
                        actions_count[action] += 1
            
            # Calculate success rates
            total_decisions = sum(decisions_count.values())
            continue_rate = (decisions_count["LANJUTKAN ORDER"] / total_decisions * 100) if total_decisions > 0 else 0
            cancel_rate = (decisions_count["CANCEL ORDER"] / total_decisions * 100) if total_decisions > 0 else 0
            
            return {
                "total_orders_reevaluated": len(self.reevaluation_history),
                "total_reevaluations": total_reevaluations,
                "decisions_breakdown": decisions_count,
                "actions_breakdown": actions_count,
                "success_rates": {
                    "continue_rate_percent": round(continue_rate, 2),
                    "cancel_rate_percent": round(cancel_rate, 2)
                },
                "average_reevaluations_per_order": round(total_reevaluations / len(self.reevaluation_history), 2) if self.reevaluation_history else 0,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting re-evaluation statistics: {e}")
            return {"error": str(e)}
    
    async def cleanup_old_reevaluations(self, days_to_keep: int = 7):
        """Clean up old re-evaluation history"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cleaned_count = 0
            
            for order_id in list(self.reevaluation_history.keys()):
                history = self.reevaluation_history[order_id]
                
                # Filter out old reevaluations
                filtered_history = []
                for reevaluation in history:
                    reevaluation_date = datetime.fromisoformat(reevaluation["reevaluation_timestamp"])
                    if reevaluation_date >= cutoff_date:
                        filtered_history.append(reevaluation)
                    else:
                        cleaned_count += 1
                
                # Update or remove history
                if filtered_history:
                    self.reevaluation_history[order_id] = filtered_history
                else:
                    del self.reevaluation_history[order_id]
            
            if cleaned_count > 0:
                logger.info(f"🧹 Cleaned up {cleaned_count} old re-evaluation records")
            
        except Exception as e:
            logger.error(f"❌ Error cleaning up old re-evaluations: {e}")
