#!/usr/bin/env python3
"""
Auto Trading System Test Suite
Test semua komponen sistem sebelum trading live
"""
import asyncio
import sys
import os
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent))

async def test_mt5_connection():
    """Test MetaTrader 5 connection"""
    print("🔌 Testing MetaTrader 5 connection...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        
        connector = MT5Connector()
        success = await connector.connect()
        
        if success:
            print("✅ MT5 connection successful")
            
            # Test account info
            account_info = await connector.get_account_info()
            if account_info:
                print(f"✅ Account info: {account_info.login} - Balance: {account_info.balance}")
            else:
                print("⚠️ Could not get account info")
            
            await connector.disconnect()
            return True
        else:
            print("❌ MT5 connection failed")
            return False
            
    except Exception as e:
        print(f"❌ MT5 test error: {e}")
        return False

async def test_gpt_connection():
    """Test GPT API connection"""
    print("🧠 Testing GPT API connection...")
    
    try:
        from backend.services.gpt_analyzer import GPTAnalyzer
        
        analyzer = GPTAnalyzer()
        
        # Test simple sentiment analysis
        result = await analyzer.get_market_sentiment(["EURUSD"])
        
        if result and "EURUSD" in result:
            print("✅ GPT API connection successful")
            print(f"✅ Sample sentiment: {result}")
            return True
        else:
            print("❌ GPT API test failed")
            return False
            
    except Exception as e:
        print(f"❌ GPT test error: {e}")
        return False

async def test_market_data():
    """Test market data retrieval"""
    print("📊 Testing market data retrieval...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5 for market data test")
            return False
        
        # Test market data for EURUSD
        market_data = await connector.get_market_data("EURUSD")
        
        if market_data:
            print(f"✅ Market data: {market_data.symbol} - Bid: {market_data.bid}, Ask: {market_data.ask}")
            
            # Test OHLCV data
            ohlcv_data = await connector.get_ohlcv_data("EURUSD", "M5", 10)
            if ohlcv_data:
                print(f"✅ OHLCV data: {len(ohlcv_data)} candles retrieved")
            else:
                print("⚠️ Could not get OHLCV data")
            
            await connector.disconnect()
            return True
        else:
            print("❌ Could not get market data")
            await connector.disconnect()
            return False
            
    except Exception as e:
        print(f"❌ Market data test error: {e}")
        return False

async def test_technical_analysis():
    """Test technical analysis"""
    print("📈 Testing technical analysis...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5 for technical analysis test")
            return False
        
        # Get OHLCV data
        ohlcv_data = await connector.get_ohlcv_data("EURUSD", "M5", 100)
        
        if not ohlcv_data:
            print("❌ Could not get OHLCV data for technical analysis")
            await connector.disconnect()
            return False
        
        # Calculate indicators
        indicators = TechnicalAnalyzer.calculate_indicators(ohlcv_data)
        
        if indicators:
            print(f"✅ Technical indicators calculated:")
            print(f"   RSI: {indicators.rsi}")
            print(f"   SMA 20: {indicators.sma_20}")
            print(f"   MACD: {indicators.macd}")
        else:
            print("⚠️ Could not calculate technical indicators")
        
        await connector.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Technical analysis test error: {e}")
        return False

async def test_risk_management():
    """Test risk management"""
    print("⚖️ Testing risk management...")
    
    try:
        from backend.utils.risk_management import RiskManager
        from backend.models.trading_models import AccountInfo, OrderRequest, OrderType
        
        risk_manager = RiskManager()
        
        # Mock account info
        account_info = AccountInfo(
            login=12345,
            balance=10000.0,
            equity=10000.0,
            margin=0.0,
            free_margin=10000.0,
            margin_level=0.0,
            profit=0.0,
            currency="USD",
            leverage=100,
            server="Demo"
        )
        
        # Test position size calculation
        position_size = risk_manager.calculate_position_size(
            symbol="EURUSD",
            account_info=account_info,
            entry_price=1.1000,
            stop_loss=1.0950,
            risk_percent=2.0
        )
        
        print(f"✅ Position size calculation: {position_size} lots")
        
        # Test order validation
        order_request = OrderRequest(
            symbol="EURUSD",
            order_type=OrderType.BUY,
            lot_size=position_size,
            stop_loss=1.0950,
            take_profit=1.1100
        )
        
        is_valid, message = risk_manager.validate_order(
            order_request, account_info, [], None
        )
        
        print(f"✅ Order validation: {is_valid} - {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Risk management test error: {e}")
        return False

async def test_full_analysis_flow():
    """Test complete analysis flow"""
    print("🔄 Testing complete analysis flow...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5 for full analysis test")
            return False
        
        # Get market data
        market_data = await connector.get_market_data("EURUSD")
        ohlcv_data = await connector.get_ohlcv_data("EURUSD", "M5", 100)
        
        if not market_data or not ohlcv_data:
            print("❌ Could not get market data for analysis")
            await connector.disconnect()
            return False
        
        # Calculate technical indicators
        indicators = TechnicalAnalyzer.calculate_indicators(ohlcv_data)
        
        # Perform GPT analysis
        analyzer = GPTAnalyzer()
        gpt_analysis = await analyzer.analyze_market(
            "EURUSD", market_data, ohlcv_data, indicators
        )
        
        if gpt_analysis:
            print(f"✅ Complete analysis successful:")
            print(f"   Signal: {gpt_analysis.signal}")
            print(f"   Confidence: {gpt_analysis.confidence}")
            print(f"   Entry: {gpt_analysis.entry_price}")
            print(f"   Reasoning: {gpt_analysis.reasoning[:100]}...")
        else:
            print("⚠️ GPT analysis failed")
        
        await connector.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Full analysis test error: {e}")
        return False

def test_configuration():
    """Test configuration"""
    print("⚙️ Testing configuration...")
    
    try:
        from backend.config import settings
        
        # Check required settings
        required_settings = [
            'openai_api_key',
            'mt5_login',
            'mt5_password',
            'mt5_server'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not getattr(settings, setting, None):
                missing_settings.append(setting)
        
        if missing_settings:
            print(f"❌ Missing configuration: {missing_settings}")
            return False
        
        print("✅ Configuration complete")
        print(f"   Trading symbols: {settings.symbols_list}")
        print(f"   Max risk: {settings.max_risk_percent}%")
        print(f"   Default lot size: {settings.default_lot_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 Auto Trading System Test Suite")
    print("=" * 50)
    
    # Test configuration first
    if not test_configuration():
        print("\n❌ Configuration test failed. Please check your .env file.")
        return False
    
    # Define test cases
    test_cases = [
        ("MetaTrader 5 Connection", test_mt5_connection),
        ("GPT API Connection", test_gpt_connection),
        ("Market Data Retrieval", test_market_data),
        ("Technical Analysis", test_technical_analysis),
        ("Risk Management", test_risk_management),
        ("Complete Analysis Flow", test_full_analysis_flow)
    ]
    
    results = []
    
    for test_name, test_func in test_cases:
        print(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for trading.")
        return True
    else:
        print("⚠️ Some tests failed. Please fix issues before trading.")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        sys.exit(1)
