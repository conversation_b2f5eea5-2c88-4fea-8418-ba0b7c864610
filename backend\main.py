"""
Main FastAPI Application
Auto Trading System Backend Server
Supports both Original Trading System and New Scalping System
"""
import asyncio
import time
import os
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger
import json
from datetime import datetime

from .config import settings
from .api.trading_routes import router as trading_router, initialize_trading_engine
from .api.scalping_routes import router as scalping_router, initialize_scalping_services
from .models.trading_models import SystemStatus
from .services.scalping_engine import ScalpingEngine

# Global scalping engine
scalping_engine = None


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(connection)

        # Remove disconnected connections
        for conn in disconnected:
            self.disconnect(conn)

    async def broadcast_positions(self, positions_data):
        """Broadcast position updates to all connected clients"""
        if not self.active_connections:
            return

        message = {
            "type": "position_update",
            "timestamp": datetime.now().isoformat(),
            "positions": positions_data
        }
        await self.broadcast(json.dumps(message))

    async def broadcast_position_change(self, action, position_data):
        """Broadcast specific position changes (open, close, update)"""
        if not self.active_connections:
            return

        message = {
            "type": "position_change",
            "action": action,  # "opened", "closed", "updated"
            "timestamp": datetime.now().isoformat(),
            "position": position_data
        }
        await self.broadcast(json.dumps(message))


manager = ConnectionManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global scalping_engine

    # Check if we should start scalping mode
    scalping_mode = os.getenv("SCALPING_MODE", "false").lower() == "true"

    if scalping_mode:
        logger.info("🎯 Starting SCALPING Trading System...")

        # Initialize scalping services
        success = initialize_scalping_services()
        if not success:
            logger.error("❌ Failed to initialize scalping services")
        else:
            logger.info("✅ Scalping services initialized successfully")

            # Start scalping engine
            scalping_engine = ScalpingEngine()
            asyncio.create_task(scalping_engine.start_engine())
            logger.info("🔄 Scalping engine started")


    else:
        logger.info("🚀 Starting Original Trading System...")

        # Initialize original trading engine
        await initialize_trading_engine()

        # Start background tasks
        asyncio.create_task(broadcast_system_status())

        logger.info("✅ Original Trading System started successfully")

    yield

    # Shutdown
    if scalping_mode and scalping_engine:
        logger.info("🛑 Shutting down Scalping System...")
        await scalping_engine.stop_engine()
    else:
        logger.info("🛑 Shutting down Original Trading System...")


# Create FastAPI app
app = FastAPI(
    title="Auto Trading System",
    description="AI-Powered Auto Trading System with GPT-4 Analysis and MetaTrader 5 Integration",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers based on mode
if os.getenv("SCALPING_MODE", "false").lower() == "true":
    # In scalping mode, use scalping router for both /api/scalping and /api/trading
    app.include_router(scalping_router)

    # Use scalping routes for /api/trading path for dashboard compatibility
    app.include_router(scalping_router, prefix="/api/trading", tags=["trading-compat"])
else:
    # In normal mode, use original trading router
    app.include_router(trading_router)


@app.get("/")
async def root():
    """Root endpoint"""
    scalping_mode = os.getenv("SCALPING_MODE", "false").lower() == "true"

    return {
        "message": "Scalping Trading System API" if scalping_mode else "Auto Trading System API",
        "mode": "scalping" if scalping_mode else "original",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "auto-trading-system",
        "timestamp": datetime.now().isoformat()
    }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()

            # Echo back for now (can be extended for specific commands)
            await manager.send_personal_message(f"Echo: {data}", websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)


async def broadcast_system_status():
    """Background task to broadcast system status"""
    logger.info("🔄 Starting broadcast_system_status background task")

    while True:
        try:
            # Import here to avoid circular imports
            from .api.trading_routes import trading_engine

            if not trading_engine:
                logger.debug("⏳ Trading engine not initialized yet, waiting...")
                await asyncio.sleep(5)
                continue

            if not manager.active_connections:
                # Only log every 60 seconds to reduce spam
                current_time = time.time()
                if not hasattr(broadcast_system_status, '_last_no_connections_log') or current_time - broadcast_system_status._last_no_connections_log > 60:
                    logger.debug("📡 No active WebSocket connections, skipping broadcast")
                    broadcast_system_status._last_no_connections_log = current_time
                await asyncio.sleep(5)
                continue

            # Only log broadcast every 30 seconds to reduce spam
            current_time = time.time()
            if not hasattr(broadcast_system_status, '_last_broadcast_log') or current_time - broadcast_system_status._last_broadcast_log > 30:
                logger.info(f"📡 Broadcasting to {len(manager.active_connections)} connections")
                broadcast_system_status._last_broadcast_log = current_time

            # Get system status
            status = await trading_engine.get_system_status()

            # Get positions with real-time P&L
            positions = await trading_engine.get_positions(realtime_pnl=True)

            # Get account info
            account_info = trading_engine.account_info

            # Prepare broadcast data with proper serialization
            status_dict = None
            if status:
                status_dict = status.dict()
                # Convert datetime to ISO string for JSON serialization
                if status_dict.get('last_analysis_time'):
                    status_dict['last_analysis_time'] = status_dict['last_analysis_time'].isoformat()

            # Serialize positions data
            positions_data = []
            for pos in positions:
                pos_dict = pos.dict() if hasattr(pos, 'dict') else pos
                # Convert datetime fields to ISO strings
                if pos_dict.get('open_time'):
                    pos_dict['open_time'] = pos_dict['open_time'].isoformat() if hasattr(pos_dict['open_time'], 'isoformat') else str(pos_dict['open_time'])
                if pos_dict.get('close_time'):
                    pos_dict['close_time'] = pos_dict['close_time'].isoformat() if hasattr(pos_dict['close_time'], 'isoformat') else str(pos_dict['close_time'])
                positions_data.append(pos_dict)

            broadcast_data = {
                "type": "system_update",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "system_status": status_dict,
                    "positions_count": len(positions),
                    "account_balance": float(account_info.balance) if account_info else 0.0,
                    "account_equity": float(account_info.equity) if account_info else 0.0,
                    "daily_profit": float(status.daily_profit) if status and hasattr(status, 'daily_profit') else 0.0
                }
            }

            # Also broadcast positions separately for position manager
            positions_broadcast = {
                "type": "position_update",
                "timestamp": datetime.now().isoformat(),
                "positions": positions_data
            }

            # Only log detailed broadcast info every 30 seconds
            if hasattr(broadcast_system_status, '_last_broadcast_log') and time.time() - broadcast_system_status._last_broadcast_log < 30:
                pass  # Skip detailed logging
            else:
                logger.info(f"📡 Broadcasting system update and {len(positions_data)} positions")

            await manager.broadcast(json.dumps(broadcast_data))
            await manager.broadcast(json.dumps(positions_broadcast))

        except Exception as e:
            logger.error(f"Error in broadcast_system_status: {e}")

        # Wait 5 seconds before next broadcast
        await asyncio.sleep(5)


@app.get("/api/symbols")
async def get_trading_symbols():
    """Get list of trading symbols"""
    return {
        "symbols": settings.symbols_list,
        "default_symbols": ["XAUUSDm", "BTCUSDm", "EURUSDm", "ETHUSDm"]
    }


@app.get("/api/config")
async def get_system_config():
    """Get system configuration (non-sensitive data only)"""
    return {
        "trading_symbols": settings.symbols_list,
        "analysis_interval_minutes": settings.analysis_interval_minutes,
        "max_open_positions": settings.max_open_positions,
        "default_lot_size": settings.default_lot_size,
        "stop_loss_pips": settings.stop_loss_pips,
        "take_profit_pips": settings.take_profit_pips,
        "max_risk_percent": settings.max_risk_percent
    }


@app.get("/api/trading/risk-status")
async def get_risk_status():
    """Get current risk management status"""
    try:
        # Get trading engine from app state
        engine = app.state.trading_engine

        if not engine or not engine.is_initialized:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        # Get account info and positions
        account_info = engine.account_info
        positions = await engine.get_positions()

        if not account_info:
            raise HTTPException(status_code=503, detail="Account info not available")

        # Calculate risk metrics
        from backend.utils.risk_management import RiskManager
        risk_manager = RiskManager()

        # Calculate drawdown
        drawdown_percent = risk_manager._calculate_drawdown_percent(account_info, positions)

        # Calculate daily loss
        daily_loss = risk_manager._calculate_daily_loss(positions)

        # Count open positions
        open_positions = [p for p in positions if p.status.value == 'OPEN']

        # Calculate unrealized P&L
        unrealized_pnl = sum(pos.profit or 0 for pos in open_positions)

        return {
            "account": {
                "balance": float(account_info.balance),
                "equity": float(account_info.equity),
                "free_margin": float(account_info.free_margin),
                "margin_level": float(account_info.margin_level),
                "profit": float(account_info.profit)
            },
            "risk_metrics": {
                "drawdown_percent": round(drawdown_percent, 2),
                "max_drawdown_percent": settings.max_drawdown_percent,
                "daily_loss": round(daily_loss, 2),
                "max_daily_loss": settings.max_daily_loss,
                "open_positions": len(open_positions),
                "max_open_positions": settings.max_open_positions,
                "unrealized_pnl": round(unrealized_pnl, 2)
            },
            "trading_allowed": {
                "drawdown_ok": drawdown_percent < settings.max_drawdown_percent,
                "daily_loss_ok": daily_loss < settings.max_daily_loss,
                "positions_ok": len(open_positions) < settings.max_open_positions,
                "overall": (
                    drawdown_percent < settings.max_drawdown_percent and
                    daily_loss < settings.max_daily_loss and
                    len(open_positions) < settings.max_open_positions
                )
            }
        }

    except Exception as e:
        logger.error(f"Error getting risk status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/trading/reset-risk-limits")
async def reset_risk_limits():
    """Temporarily reset risk limits (EMERGENCY USE ONLY)"""
    try:
        # Get trading engine from app state
        engine = app.state.trading_engine

        if not engine or not engine.is_initialized:
            raise HTTPException(status_code=503, detail="Trading engine not initialized")

        # This is an emergency function - use with caution
        logger.warning("🚨 EMERGENCY: Risk limits temporarily reset!")

        # You can implement temporary risk limit override here
        # For now, we'll just return current status

        return {
            "message": "Risk limits reset temporarily",
            "warning": "This is an emergency function - use with extreme caution!",
            "timestamp": datetime.now().isoformat(),
            "note": "Consider closing losing positions or adjusting .env settings instead"
        }

    except Exception as e:
        logger.error(f"Error resetting risk limits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn

    # Configure logging
    logger.add(
        settings.log_file,
        rotation="1 day",
        retention="30 days",
        level=settings.log_level
    )

    logger.info("Starting Auto Trading System API Server...")

    uvicorn.run(
        "backend.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        log_level=settings.log_level.lower()
    )
