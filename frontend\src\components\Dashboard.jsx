import React, { useState, useEffect } from 'react';
import {
  Play,
  Square,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { tradingApi, scalpingApi, getActiveApi, formatCurrency, formatDateTime, wsService } from '../services/api';
import SymbolSelector from './SymbolSelector';

const Dashboard = ({ systemStatus }) => {
  const [accountInfo, setAccountInfo] = useState(null);
  const [positions, setPositions] = useState([]);
  const [isEngineRunning, setIsEngineRunning] = useState(false);
  const [loading, setLoading] = useState(false);
  const [systemType, setSystemType] = useState('unknown'); // 'trading', 'scalping', or 'unknown'
  const [activeApi, setActiveApi] = useState(null);
  const [realtimeData, setRealtimeData] = useState({
    balance: 0,
    equity: 0,
    dailyProfit: 0,
    positionsCount: 0
  });

  useEffect(() => {
    detectSystemAndLoadData();

    // Listen for real-time updates
    wsService.addListener('system_update', handleRealtimeUpdate);

    return () => {
      wsService.removeListener('system_update', handleRealtimeUpdate);
    };
  }, []);

  useEffect(() => {
    if (systemStatus) {
      setIsEngineRunning(systemStatus.is_trading_enabled || false);
    }
  }, [systemStatus]);

  const handleRealtimeUpdate = (data) => {
    if (data.data) {
      setRealtimeData({
        balance: data.data.account_balance || 0,
        equity: data.data.account_equity || 0,
        dailyProfit: data.data.daily_profit || 0,
        positionsCount: data.data.positions_count || 0
      });
    }
  };

  const detectSystemAndLoadData = async () => {
    try {
      setLoading(true);

      // Detect which system is running
      const { type, api } = await getActiveApi();
      setSystemType(type);
      setActiveApi(api);

      if (api) {
        await loadDashboardData(api, type);
      } else {
        console.warn('No trading system detected');
      }
    } catch (error) {
      console.error('Error detecting system:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async (api = activeApi, type = systemType) => {
    if (!api) return;

    try {
      setLoading(true);

      if (type === 'scalping') {
        // For scalping system, we don't have account/positions endpoints
        // Just get system status
        const statusResponse = await api.getSystemStatus().catch(() => ({ data: null }));
        if (statusResponse.data?.data) {
          setIsEngineRunning(statusResponse.data.data.engine_running || false);
        }
        setPositions([]); // Scalping system doesn't expose positions the same way
      } else {
        // For original trading system
        const [accountResponse, positionsResponse] = await Promise.all([
          api.getAccountInfo().catch(() => ({ data: null })),
          api.getPositions().catch(() => ({ data: [] }))
        ]);

        setAccountInfo(accountResponse.data);
        setPositions(positionsResponse.data);
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartEngine = async () => {
    if (!activeApi) {
      alert('No trading system detected. Please ensure the backend is running.');
      return;
    }

    try {
      setLoading(true);

      let response;
      if (systemType === 'scalping') {
        response = await activeApi.startEngine();
      } else {
        response = await activeApi.startEngine(true);
      }

      // Handle scalping system response
      if (systemType === 'scalping') {
        const data = response.data;
        if (data.success || data.message === "Engine already running") {
          setIsEngineRunning(true);
          console.log('✅ Scalping engine started or already running');
        } else {
          throw new Error(data.message || 'Failed to start scalping engine');
        }
      } else {
        // Original trading system
        setIsEngineRunning(true);
      }

      // Reload data after starting
      setTimeout(() => loadDashboardData(), 2000);
    } catch (error) {
      console.error('Error starting engine:', error);
      const systemName = systemType === 'scalping' ? 'scalping' : 'trading';

      // Check if it's already running error
      if (error.response?.data?.message === "Engine already running") {
        setIsEngineRunning(true);
        console.log('✅ Engine was already running');
      } else {
        alert(`Failed to start ${systemName} engine: ` + (error.response?.data?.message || error.message));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleStopEngine = async () => {
    if (!activeApi) {
      alert('No trading system detected.');
      return;
    }

    try {
      setLoading(true);
      await activeApi.stopEngine();
      setIsEngineRunning(false);
    } catch (error) {
      console.error('Error stopping engine:', error);
      const systemName = systemType === 'scalping' ? 'scalping' : 'trading';
      alert(`Failed to stop ${systemName} engine: ` + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getDisplayBalance = () => {
    return realtimeData.balance || accountInfo?.balance || 0;
  };

  const getDisplayEquity = () => {
    return realtimeData.equity || accountInfo?.equity || 0;
  };

  const getDisplayDailyProfit = () => {
    return realtimeData.dailyProfit || systemStatus?.daily_profit || 0;
  };

  const getDisplayPositionsCount = () => {
    return realtimeData.positionsCount || positions.length || 0;
  };

  const stats = [
    {
      name: 'Account Balance',
      value: formatCurrency(getDisplayBalance()),
      icon: DollarSign,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      name: 'Account Equity',
      value: formatCurrency(getDisplayEquity()),
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      name: 'Daily P&L',
      value: formatCurrency(getDisplayDailyProfit()),
      icon: getDisplayDailyProfit() >= 0 ? TrendingUp : TrendingDown,
      color: getDisplayDailyProfit() >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: getDisplayDailyProfit() >= 0 ? 'bg-green-50' : 'bg-red-50'
    },
    {
      name: 'Open Positions',
      value: getDisplayPositionsCount(),
      icon: Activity,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Engine Control */}
      <div className="card">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">
              {systemType === 'scalping' ? 'Scalping Engine Control' :
               systemType === 'trading' ? 'Trading Engine Control' :
               'Engine Control'}
            </h2>
            <p className="text-sm text-gray-600">
              {systemType === 'scalping' ? 'Start or stop the scalping trading system' :
               systemType === 'trading' ? 'Start or stop the automated trading system' :
               'No trading system detected'}
            </p>
            {systemType !== 'unknown' && (
              <p className="text-xs text-blue-600 mt-1">
                System: {systemType.charAt(0).toUpperCase() + systemType.slice(1)}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  isEngineRunning ? 'bg-green-500' : 'bg-red-500'
                }`}
              ></div>
              <span className="text-sm font-medium">
                {isEngineRunning ? 'Running' : 'Stopped'}
              </span>
            </div>
            {systemType === 'unknown' ? (
              <button
                onClick={detectSystemAndLoadData}
                disabled={loading}
                className="btn btn-secondary flex items-center space-x-2"
              >
                <AlertTriangle className="w-4 h-4" />
                <span>Detect System</span>
              </button>
            ) : !isEngineRunning ? (
              <button
                onClick={handleStartEngine}
                disabled={loading}
                className="btn btn-success flex items-center space-x-2"
              >
                <Play className="w-4 h-4" />
                <span>Start Engine</span>
              </button>
            ) : (
              <button
                onClick={handleStopEngine}
                disabled={loading}
                className="btn btn-danger flex items-center space-x-2"
              >
                <Square className="w-4 h-4" />
                <span>Stop Engine</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Symbol Selection */}
      <SymbolSelector />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* System Status */}
      {systemStatus && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">System Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              {systemStatus.is_mt5_connected ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-500" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">MetaTrader 5</p>
                <p className="text-sm text-gray-600">
                  {systemStatus.is_mt5_connected ? 'Connected' : 'Disconnected'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {systemStatus.is_gpt_available ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-500" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">GPT Analysis</p>
                <p className="text-sm text-gray-600">
                  {systemStatus.is_gpt_available ? 'Available' : 'Unavailable'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {systemStatus.system_health === 'HEALTHY' ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">System Health</p>
                <p className="text-sm text-gray-600">{systemStatus.system_health}</p>
              </div>
            </div>
          </div>

          {systemStatus.last_analysis_time && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600">
                Last Analysis: {formatDateTime(systemStatus.last_analysis_time)}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Recent Positions */}
      {positions.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Positions</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    P&L
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {positions.slice(0, 5).map((position) => (
                  <tr key={position.ticket || position.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {position.symbol}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.order_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.lot_size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.entry_price?.toFixed(5)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`${
                          (position.profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {formatCurrency(position.profit || 0)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`status-indicator ${
                          position.status === 'OPEN'
                            ? 'status-healthy'
                            : position.status === 'CLOSED'
                            ? 'bg-gray-100 text-gray-800'
                            : 'status-warning'
                        }`}
                      >
                        {position.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
