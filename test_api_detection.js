// Test script to verify API detection works
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8001';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Scalping API endpoints
const scalpingApi = {
  healthCheck: () => api.get('/api/scalping/health'),
  getSystemStatus: () => api.get('/api/scalping/status'),
  startEngine: () => api.post('/api/scalping/start'),
  stopEngine: () => api.post('/api/scalping/stop'),
};

// Trading API endpoints  
const tradingApi = {
  healthCheck: () => api.get('/api/health'),
  getSystemStatus: () => api.get('/api/trading/status'),
  startEngine: (autoTradingEnabled = true) => 
    api.post('/api/trading/start', { auto_trading_enabled: autoTradingEnabled }),
  stopEngine: () => api.post('/api/trading/stop'),
};

// Auto-detect which system is running
async function getActiveApi() {
  try {
    console.log('🔍 Testing scalping system...');
    const scalpingHealth = await scalpingApi.healthCheck();
    if (scalpingHealth.status === 200) {
      console.log('✅ Scalping system detected');
      return { type: 'scalping', api: scalpingApi };
    }
  } catch (error) {
    console.log('❌ Scalping system not available:', error.message);
    
    // Try original system
    try {
      console.log('🔍 Testing trading system...');
      const tradingHealth = await tradingApi.healthCheck();
      if (tradingHealth.status === 200) {
        console.log('✅ Trading system detected');
        return { type: 'trading', api: tradingApi };
      }
    } catch (error) {
      console.log('❌ Trading system not available:', error.message);
    }
  }
  
  console.log('❌ No system detected');
  return { type: 'none', api: null };
}

async function testSystem() {
  console.log('🚀 Testing API Detection...\n');
  
  const { type, api } = await getActiveApi();
  
  if (!api) {
    console.log('❌ No trading system available');
    return;
  }
  
  console.log(`\n📊 System Type: ${type}`);
  
  try {
    console.log('🔍 Getting system status...');
    const statusResponse = await api.getSystemStatus();
    console.log('✅ Status Response:', JSON.stringify(statusResponse.data, null, 2));
    
    if (type === 'scalping') {
      const isRunning = statusResponse.data?.data?.engine_running;
      console.log(`🔄 Engine Running: ${isRunning}`);
      
      if (isRunning) {
        console.log('✅ Scalping engine is already running');
      } else {
        console.log('🚀 Attempting to start scalping engine...');
        const startResponse = await api.startEngine();
        console.log('✅ Start Response:', JSON.stringify(startResponse.data, null, 2));
      }
    }
    
  } catch (error) {
    console.log('❌ Error testing system:', error.message);
    if (error.response) {
      console.log('Response data:', error.response.data);
    }
  }
}

testSystem().catch(console.error);
